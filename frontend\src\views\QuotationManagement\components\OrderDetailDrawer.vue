<template>
    <div class="order-detail-drawer" v-loading="loading">
        <div v-if="orderDetail" class="order-content">
            <!-- 基本信息 -->
            <el-card class="detail-card" shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="card-title">基本信息</span>
                        <el-tag :type="getStatusTagType(orderDetail.status)">
                            {{ getStatusText(orderDetail.status) }}
                        </el-tag>
                    </div>
                </template>
                
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="订单编号">
                        <el-tag type="primary">{{ orderDetail.orderNo }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="询价单号">
                        {{ orderDetail.quoteNo || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="客户名称">
                        <el-link type="primary" @click="viewCustomer">{{ orderDetail.customerName }}</el-link>
                    </el-descriptions-item>
                    <el-descriptions-item label="联系人">
                        {{ orderDetail.contactPerson || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                        {{ orderDetail.contactPhone || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="收货地址">
                        {{ orderDetail.deliveryAddress || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订单金额">
                        <span style="color: #e6a23c; font-weight: bold; font-size: 16px;">
                            {{ orderDetail.currency || 'CNY' }} {{ formatCurrency(orderDetail.totalAmount) }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item label="下单日期">
                        {{ formatDate(orderDetail.orderDate) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="负责人">
                        {{ orderDetail.ownerName || '未分配' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="预期交付">
                        {{ formatDate(orderDetail.expectedDeliveryDate) }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>

            <!-- 订单项目详情 -->
            <el-card class="detail-card" shadow="never">
                <template #header>
                    <span class="card-title">订单项目 ({{ orderDetail.orderItems?.length || 0 }}个模型)</span>
                </template>
                
                <el-table :data="orderDetail.orderItems" border style="width: 100%">
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column prop="productName" label="模型名称" min-width="150">
                        <template #default="scope">
                            <div class="model-info">
                                <div class="model-name">{{ scope.row.productName || scope.row.modelName }}</div>
                                <div class="model-file" v-if="scope.row.modelFileUrl">
                                    <el-link type="primary" :href="scope.row.modelFileUrl" target="_blank">
                                        <el-icon><Download /></el-icon>
                                        查看模型文件
                                    </el-link>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="模型信息" min-width="180">
                        <template #default="scope">
                            <div class="model-specs">
                                <div v-if="scope.row.dimensions">
                                    <strong>尺寸:</strong> {{ scope.row.dimensions }}
                                </div>
                                <div v-if="scope.row.volume">
                                    <strong>体积:</strong> {{ scope.row.volume }}
                                </div>
                                <div v-if="scope.row.surfaceArea">
                                    <strong>表面积:</strong> {{ scope.row.surfaceArea }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="materialName" label="材料" min-width="120" />
                    <el-table-column prop="quantity" label="数量" width="80" align="center" />
                    <el-table-column prop="unitPrice" label="单价" width="100" align="right">
                        <template #default="scope">
                            {{ formatCurrency(scope.row.unitPrice) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="subtotal" label="小计" width="120" align="right">
                        <template #default="scope">
                            <span style="color: #e6a23c; font-weight: 500;">
                                {{ formatCurrency(scope.row.subtotal || scope.row.totalPrice) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="processOptions" label="后处理" min-width="120">
                        <template #default="scope">
                            <el-tag v-if="scope.row.processOptions" size="small">
                                {{ scope.row.processOptions }}
                            </el-tag>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 关联信息 -->
            <el-row :gutter="16">
                <el-col :span="12">
                    <el-card class="detail-card" shadow="never">
                        <template #header>
                            <span class="card-title">商机信息</span>
                        </template>
                        
                        <div v-if="orderDetail.opportunity">
                            <el-descriptions :column="1" size="small">
                                <el-descriptions-item label="商机名称">
                                    <el-link type="primary" @click="viewOpportunity">
                                        {{ orderDetail.opportunity.opportunityName }}
                                    </el-link>
                                </el-descriptions-item>
                                <el-descriptions-item label="商机阶段">
                                    {{ orderDetail.opportunity.stage }}
                                </el-descriptions-item>
                                <el-descriptions-item label="预期成交金额">
                                    {{ formatCurrency(orderDetail.opportunity.expectedAmount) }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                        <div v-else class="no-data">
                            <el-empty description="暂无关联商机" :image-size="60" />
                        </div>
                    </el-card>
                </el-col>
                
                <el-col :span="12">
                    <el-card class="detail-card" shadow="never">
                        <template #header>
                            <span class="card-title">合同信息</span>
                        </template>
                        
                        <div v-if="orderDetail.contract">
                            <el-descriptions :column="1" size="small">
                                <el-descriptions-item label="合同编号">
                                    <el-link type="primary" @click="viewContract">
                                        {{ orderDetail.contract.contractNo }}
                                    </el-link>
                                </el-descriptions-item>
                                <el-descriptions-item label="合同金额">
                                    {{ formatCurrency(orderDetail.contract.totalAmount) }}
                                </el-descriptions-item>
                                <el-descriptions-item label="签订日期">
                                    {{ formatDate(orderDetail.contract.signDate) }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                        <div v-else class="no-data">
                            <el-empty description="暂无关联合同" :image-size="60" />
                        </div>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 备注信息 -->
            <el-card class="detail-card" shadow="never" v-if="orderDetail.remarks">
                <template #header>
                    <span class="card-title">备注信息</span>
                </template>
                
                <div class="remarks-content">
                    {{ orderDetail.remarks }}
                </div>
            </el-card>
        </div>

        <div v-else-if="!loading" class="no-data">
            <el-empty description="订单信息不存在" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { get3DPrintingOrderDetail } from '@/api/crm/order'

interface Props {
    orderId: number | null
}

const props = defineProps<Props>()
const emit = defineEmits(['close'])

const loading = ref(false)
const orderDetail = ref(null)

// 监听orderId变化
watch(() => props.orderId, (newId) => {
    if (newId) {
        loadOrderDetail(newId)
    }
}, { immediate: true })

const loadOrderDetail = async (orderId: number) => {
    loading.value = true
    try {
        const response = await get3DPrintingOrderDetail(orderId)
        if (response && 'code' in response && response.code === 200) {
            orderDetail.value = (response as any).data
        } else {
            ElMessage.error((response as any)?.msg || '加载订单详情失败')
        }
    } catch (error) {
        ElMessage.error('加载订单详情失败')
    } finally {
        loading.value = false
    }
}

// 辅助函数
const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return isNaN(num) ? '0.00' : num.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

const formatDate = (date: string) => {
    if (!date) return '-'
    return new Date(date).toLocaleDateString('zh-CN')
}

const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        'PENDING': '待处理',
        'CONFIRMED': '已确认',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
    }
    return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
    const typeMap: Record<string, string> = {
        'PENDING': 'warning',
        'CONFIRMED': 'primary',
        'IN_PROGRESS': 'success',
        'COMPLETED': 'success',
        'CANCELLED': 'danger'
    }
    return typeMap[status] || 'info'
}

// 查看关联信息
const viewCustomer = () => {
    ElMessage.info('跳转到客户详情功能开发中')
}

const viewOpportunity = () => {
    ElMessage.info('跳转到商机详情功能开发中')
}

const viewContract = () => {
    ElMessage.info('跳转到合同详情功能开发中')
}

</script>

<style scoped>
.order-detail-drawer {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.order-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.detail-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-weight: 600;
    font-size: 16px;
    color: #303133;
}

.model-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.model-name {
    font-weight: 500;
    color: #303133;
}

.model-file {
    font-size: 12px;
}

.model-specs {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 13px;
    color: #606266;
}

.model-specs strong {
    color: #303133;
}

.no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    color: #909399;
}

.remarks-content {
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 6px;
    color: #606266;
    line-height: 1.6;
}

:deep(.el-descriptions__label) {
    font-weight: 500;
    color: #606266;
}

:deep(.el-descriptions__content) {
    color: #303133;
}
</style>
<template>
  <el-dialog 
    title="订单详情" 
    :visible.sync="dialogVisible" 
    width="80%" 
    :before-close="handleClose"
    append-to-body
  >
    <div v-loading="loading">
      <!-- 订单基本信息 -->
      <el-card class="mb-3">
        <div slot="header" class="clearfix">
          <span class="card-title">基本信息</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text"
            @click="handleEdit"
            v-hasPermi="['crm:order:edit']"
          >编辑</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单编号：</label>
              <span>{{ orderDetail.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单状态：</label>
              <dict-tag :options="dict.type.crm_order_status" :value="orderDetail.status"/>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单金额：</label>
              <span class="text-danger">¥{{ orderDetail.totalAmount }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>客户名称：</label>
              <span>{{ orderDetail.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>客户电话：</label>
              <span>{{ orderDetail.customerPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>客户邮箱：</label>
              <span>{{ orderDetail.customerEmail || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单来源：</label>
              <dict-tag :options="dict.type.crm_order_source" :value="orderDetail.orderSource"/>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单类型：</label>
              <span>{{ orderDetail.orderType }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>优先级：</label>
              <el-tag v-if="orderDetail.priorityLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
              <el-tag v-else-if="orderDetail.priorityLevel === 'HIGH'" type="warning" size="mini">高</el-tag>
              <el-tag v-else-if="orderDetail.priorityLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
              <el-tag v-else type="info" size="mini">低</el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>分配状态：</label>
              <el-tag v-if="orderDetail.assignmentStatus === 'UNASSIGNED'" type="info">未分配</el-tag>
              <el-tag v-else-if="orderDetail.assignmentStatus === 'ASSIGNED'" type="success">已分配</el-tag>
              <el-tag v-else-if="orderDetail.assignmentStatus === 'TRANSFERRED'" type="warning">已转移</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人：</label>
              <span>{{ orderDetail.ownerName || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>分配时间：</label>
              <span>{{ parseTime(orderDetail.assignedTime) || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ parseTime(orderDetail.createTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ parseTime(orderDetail.updateTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>创建人：</label>
              <span>{{ orderDetail.createBy }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="orderDetail.remarks">
          <el-col :span="24">
            <div class="info-item">
              <label>备注：</label>
              <span>{{ orderDetail.remarks }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 订单明细 -->
      <el-card class="mb-3">
        <div slot="header" class="clearfix">
          <span class="card-title">订单明细</span>
        </div>
        <el-table :data="orderDetail.orderItems" border>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="产品名称" prop="productName" align="center" />
          <el-table-column label="产品编码" prop="productCode" align="center" />
          <el-table-column label="规格型号" prop="specification" align="center" />
          <el-table-column label="数量" prop="quantity" align="center" width="80" />
          <el-table-column label="单位" prop="unit" align="center" width="80" />
          <el-table-column label="单价" prop="unitPrice" align="center" width="100">
            <template slot-scope="scope">
              <span class="text-danger">¥{{ scope.row.unitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column label="折扣率" prop="discountRate" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.discountRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="小计" prop="subtotal" align="center" width="100">
            <template slot-scope="scope">
              <span class="text-danger">¥{{ scope.row.subtotal }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remarks" align="center" />
        </el-table>
        
        <!-- 金额汇总 -->
        <div class="amount-summary">
          <el-row :gutter="20">
            <el-col :span="18"></el-col>
            <el-col :span="6">
              <div class="summary-item">
                <label>商品总额：</label>
                <span class="amount">¥{{ orderDetail.subtotalAmount || orderDetail.totalAmount }}</span>
              </div>
              <div class="summary-item" v-if="orderDetail.discountAmount">
                <label>优惠金额：</label>
                <span class="amount text-success">-¥{{ orderDetail.discountAmount }}</span>
              </div>
              <div class="summary-item" v-if="orderDetail.shippingFee">
                <label>运费：</label>
                <span class="amount">¥{{ orderDetail.shippingFee }}</span>
              </div>
              <div class="summary-item total">
                <label>订单总额：</label>
                <span class="amount text-danger">¥{{ orderDetail.totalAmount }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 客户匹配信息 -->
      <el-card class="mb-3" v-if="customerMatchInfo">
        <div slot="header" class="clearfix">
          <span class="card-title">客户匹配信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>匹配结果：</label>
              <el-tag v-if="customerMatchInfo.isNewCustomer" type="success">新客户</el-tag>
              <el-tag v-else type="warning">老客户</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>匹配方式：</label>
              <span>{{ customerMatchInfo.matchType }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>置信度：</label>
              <span>{{ customerMatchInfo.confidence }}%</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作历史 -->
      <el-card>
        <div slot="header" class="clearfix">
          <span class="card-title">操作历史</span>
        </div>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in operationHistory"
            :key="index"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ activity.title }}</div>
              <div class="timeline-desc">{{ activity.description }}</div>
              <div class="timeline-operator">操作人：{{ activity.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
      <el-button 
        type="primary" 
        @click="handleEdit"
        v-hasPermi="['crm:order:edit']"
      >编 辑</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getOrder } from "@/api/crm/order";

export default {
  name: "OrderDetailDialog",
  dicts: ['crm_order_source', 'crm_order_status'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      orderDetail: {},
      customerMatchInfo: null,
      operationHistory: []
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    orderId: {
      handler(newVal) {
        if (newVal && this.visible) {
          this.getOrderDetail();
        }
      },
      immediate: true
    },
    visible(newVal) {
      if (newVal && this.orderId) {
        this.getOrderDetail();
      }
    }
  },
  methods: {
    /** 获取订单详情 */
    getOrderDetail() {
      if (!this.orderId) return;
      
      this.loading = true;
      getOrder(this.orderId).then(response => {
        this.orderDetail = response.data;
        this.loadCustomerMatchInfo();
        this.loadOperationHistory();
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 加载客户匹配信息 */
    loadCustomerMatchInfo() {
      // TODO: 调用客户匹配API
      this.customerMatchInfo = {
        isNewCustomer: true,
        matchType: '电话号码匹配',
        confidence: 95
      };
    },
    
    /** 加载操作历史 */
    loadOperationHistory() {
      // TODO: 调用操作历史API
      this.operationHistory = [
        {
          timestamp: '2025-02-02 10:30:00',
          type: 'primary',
          title: '订单创建',
          description: '订单创建成功，等待分配处理',
          operator: '系统'
        },
        {
          timestamp: '2025-02-02 11:00:00',
          type: 'success',
          title: '订单分配',
          description: '订单已分配给张三处理',
          operator: '管理员'
        }
      ];
    },
    
    /** 编辑订单 */
    handleEdit() {
      this.$emit('edit', this.orderDetail);
      this.handleClose();
    },
    
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.orderDetail = {};
      this.customerMatchInfo = null;
      this.operationHistory = [];
    }
  }
};
</script>

<style scoped>
.card-title {
  font-weight: bold;
  font-size: 16px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-success {
  color: #67c23a;
}

.mb-3 {
  margin-bottom: 20px;
}

.amount-summary {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.summary-item.total {
  font-size: 16px;
  font-weight: bold;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}

.amount {
  font-weight: bold;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.timeline-desc {
  color: #606266;
  margin-bottom: 5px;
}

.timeline-operator {
  color: #909399;
  font-size: 12px;
}
</style>

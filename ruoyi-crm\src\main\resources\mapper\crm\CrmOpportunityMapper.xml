<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmOpportunityMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmOpportunity" id="CrmOpportunityResult">
        <id property="id" column="id"/>
        <result property="managerId" column="manager_id"/>
        <result property="opportunityName" column="opportunity_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerId" column="customer_id"/>
        <result property="opportunityAmount" column="opportunity_amount"/>
        <result property="opportunityStage" column="opportunity_stage"/>
        <result property="winRate" column="win_rate"/>
        <result property="expectedCloseDate" column="expected_close_date"/>
        <result property="opportunitySource" column="opportunity_source"/>
        <result property="opportunityType" column="opportunity_type"/>
        <result property="remarks" column="remarks"/>
        <result property="nextContactTime" column="next_contact_time"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="created_at"/>
        <result property="updateTime" column="updated_at"/>
    </resultMap>
    
    <sql id="selectOpportunityVo">
        select id, manager_id, opportunity_name, customer_name, customer_id,
        opportunity_amount, opportunity_stage, win_rate, expected_close_date, 
        opportunity_source, opportunity_type, remarks, created_at, updated_at
        from crm_business_opportunities
    </sql>
    
    <select id="selectOpportunityList" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity" resultMap="CrmOpportunityResult">
        <include refid="selectOpportunityVo"/>
        <where>
            1=1
            <if test="managerId != null">
                AND manager_id = #{managerId}
            </if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">
                AND manager_id = #{responsiblePersonId}
            </if>
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="opportunityName != null and opportunityName != ''">
                AND opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="opportunityStage != null and opportunityStage != ''">
                AND opportunity_stage = #{opportunityStage}
            </if>
            <if test="opportunitySource != null and opportunitySource != ''">
                AND opportunity_source = #{opportunitySource}
            </if>
            <if test="opportunityType != null and opportunityType != ''">
                AND opportunity_type = #{opportunityType}
            </if>
            <if test="expectedCloseDate != null">
                AND expected_close_date = #{expectedCloseDate}
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectCrmOpportunityById" parameterType="Long" resultMap="CrmOpportunityResult">
        <include refid="selectOpportunityVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmOpportunity" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_opportunities (
            manager_id, opportunity_name, customer_name, customer_id,
            opportunity_amount, opportunity_stage, win_rate, expected_close_date, 
            opportunity_source, opportunity_type, remarks
        ) values (
            #{managerId}, #{opportunityName}, #{customerName}, #{customerId},
            #{opportunityAmount}, #{opportunityStage}, #{winRate}, #{expectedCloseDate}, 
            #{opportunitySource}, #{opportunityType}, #{remarks}
        )
    </insert>

    <update id="updateCrmOpportunity" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity">
        update crm_business_opportunities
        <trim prefix="SET" suffixOverrides=",">
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="opportunityName != null">opportunity_name = #{opportunityName},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="opportunityAmount != null">opportunity_amount = #{opportunityAmount},</if>
            <if test="opportunityStage != null">opportunity_stage = #{opportunityStage},</if>
            <if test="winRate != null">win_rate = #{winRate},</if>
            <if test="expectedCloseDate != null">expected_close_date = #{expectedCloseDate},</if>
            <if test="opportunitySource != null">opportunity_source = #{opportunitySource},</if>
            <if test="opportunityType != null">opportunity_type = #{opportunityType},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            updated_at = CURRENT_TIMESTAMP,
        </trim>
        where id = #{id}
    </update>

    <update id="updateOpportunityStage">
        update crm_business_opportunities
        set opportunity_stage = #{stage},
            win_rate = #{winRate},
            updated_at = CURRENT_TIMESTAMP
        where id = #{opportunityId}
    </update>

    <delete id="deleteCrmOpportunityById">
        delete from crm_business_opportunities where id = #{id}
    </delete>

    <delete id="deleteCrmOpportunityByIds">
        delete from crm_business_opportunities where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMyOpportunityList" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity" resultMap="CrmOpportunityResult">
        <include refid="selectOpportunityVo"/>
        <where>
            manager_id = #{managerId}
            <if test="opportunityName != null and opportunityName != ''">
                AND opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="opportunityStage != null and opportunityStage != ''">
                AND opportunity_stage = #{opportunityStage}
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectSubordinateOpportunityList" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity" resultMap="CrmOpportunityResult">
        <include refid="selectOpportunityVo"/>
        <where>
            manager_id IN (
                SELECT user_id FROM sys_user WHERE dept_id IN (
                    SELECT dept_id FROM sys_user WHERE user_id = #{managerId}
                )
            )
            <if test="opportunityName != null and opportunityName != ''">
                AND opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="opportunityStage != null and opportunityStage != ''">
                AND opportunity_stage = #{opportunityStage}
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectFollowedOpportunityList" parameterType="com.ruoyi.common.domain.entity.CrmOpportunity" resultMap="CrmOpportunityResult">
        <include refid="selectOpportunityVo"/>
        <where>
            id IN (
                SELECT opportunity_id FROM crm_business_opportunity_followers 
                WHERE user_id = #{managerId}
            )
            <if test="opportunityName != null and opportunityName != ''">
                AND opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="opportunityStage != null and opportunityStage != ''">
                AND opportunity_stage = #{opportunityStage}
            </if>
        </where>
        order by created_at desc
    </select>

</mapper>
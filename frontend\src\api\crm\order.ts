import request from '@/utils/request'

/**
 * 3D打印订单管理 API
 */

// 获取3D打印订单列表
export function get3DPrintingOrders(data: any) {
  return request({
    url: '/crm/order/quote/3d-orders',
    method: 'post',
    data
  })
}

// 获取3D打印订单详情
export function get3DPrintingOrderDetail(orderId: number) {
  return request({
    url: `/crm/order/quote/3d-order/${orderId}`,
    method: 'get'
  })
}

// 获取3D打印订单统计信息
export function get3DPrintingOrderStats() {
  return request({
    url: '/crm/order/quote/3d-orders/stats',
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(orderId: number, data: any) {
  return request({
    url: `/crm/order/status/${orderId}`,
    method: 'put',
    params: data
  })
}

// 分配订单
export function assignOrder(data: any) {
  return request({
    url: '/crm/order/assign',
    method: 'post',
    data
  })
}

// 批量分配订单
export function batchAssignOrders(data: any) {
  return request({
    url: '/crm/order/batch/assign',
    method: 'post',
    data
  })
}

// 获取我的订单列表
export function getMyOrders(data: any) {
  return request({
    url: '/crm/order/my',
    method: 'post',
    data
  })
}

// 获取未分配订单列表
export function getUnassignedOrders(data: any) {
  return request({
    url: '/crm/order/unassigned',
    method: 'post',
    data
  })
}

// 订单转合同
export function convertOrderToContract(orderId: number, contractData: any) {
  return request({
    url: `/crm/order/${orderId}/convert-contract`,
    method: 'post',
    data: contractData
  })
}

// 批量订单转合同
export function batchConvertOrdersToContract(orderIds: number[], contractData: any) {
  return request({
    url: '/crm/order/batch/convert-contract',
    method: 'post',
    data: {
      orderIds,
      contractData
    }
  })
}

// 获取订单分配历史
export function getOrderAssignmentHistory(orderId: number) {
  return request({
    url: `/crm/order/${orderId}/assignment-history`,
    method: 'get'
  })
}

// 导出订单数据
export function exportOrderData(params: any) {
  return request({
    url: '/crm/order/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/crm/order/list',
    method: 'get',
    params: query
  })
}

// 分页查询订单列表
export function pageOrder(data) {
  return request({
    url: '/crm/order/page',
    method: 'post',
    data: data
  })
}

// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/crm/order/' + id,
    method: 'get'
  })
}

// 获取订单详细信息（包含关联信息）
export function getOrderDetail(id) {
  return request({
    url: '/crm/order/detail/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/crm/order',
    method: 'post',
    data: data
  })
}

// 创建订单（包含订单项）
export function createOrder(data) {
  return request({
    url: '/crm/order/create',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/crm/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/crm/order/' + id,
    method: 'delete'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status) {
  return request({
    url: '/crm/order/status/' + id,
    method: 'put',
    params: { status }
  })
}

// 批量更新订单状态
export function batchUpdateOrderStatus(ids, status) {
  return request({
    url: '/crm/order/batch/status',
    method: 'put',
    params: { ids: ids.join(','), status }
  })
}

// 分配订单
export function assignOrder(data) {
  return request({
    url: '/crm/order/assign',
    method: 'post',
    data: data
  })
}

// 批量分配订单
export function batchAssignOrders(data) {
  return request({
    url: '/crm/order/batch/assign',
    method: 'post',
    data: data
  })
}

// 转移订单
export function transferOrder(data) {
  return request({
    url: '/crm/order/transfer',
    method: 'post',
    data: data
  })
}

// 抢单
export function grabOrder(orderId) {
  return request({
    url: '/crm/order/grab/' + orderId,
    method: 'post'
  })
}

// 回收订单
export function reclaimOrder(orderId, reason) {
  return request({
    url: '/crm/order/reclaim',
    method: 'post',
    params: { orderId, reason }
  })
}

// 获取我的订单列表
export function getMyOrders(data) {
  return request({
    url: '/crm/order/my',
    method: 'post',
    data: data
  })
}

// 获取未分配订单列表
export function getUnassignedOrders(data) {
  return request({
    url: '/crm/order/unassigned',
    method: 'post',
    data: data
  })
}

// 获取部门订单列表
export function getDeptOrders(deptId, data) {
  return request({
    url: '/crm/order/dept/' + deptId,
    method: 'post',
    data: data
  })
}

// 商机转订单
export function convertOpportunityToOrder(opportunityId, data) {
  return request({
    url: '/crm/order/convert/opportunity/' + opportunityId,
    method: 'post',
    data: data
  })
}

// 订单转合同
export function convertOrderToContract(orderId, data) {
  return request({
    url: '/crm/order/convert/contract/' + orderId,
    method: 'post',
    data: data
  })
}

// 获取订单统计信息
export function getOrderStatistics(userId) {
  return request({
    url: '/crm/order/statistics',
    method: 'get',
    params: { userId }
  })
}

// 获取订单趋势数据
export function getOrderTrendData(startDate, endDate, groupBy) {
  return request({
    url: '/crm/order/trend',
    method: 'get',
    params: { startDate, endDate, groupBy }
  })
}

// 获取订单分配历史
export function getOrderAssignmentHistory(orderId) {
  return request({
    url: '/crm/order/assignment/history/' + orderId,
    method: 'get'
  })
}

// 检查订单是否可以分配
export function checkCanAssign(orderId, userId) {
  return request({
    url: '/crm/order/check/assign',
    method: 'get',
    params: { orderId, userId }
  })
}

// 检查用户是否可以抢单
export function checkCanGrab(orderId) {
  return request({
    url: '/crm/order/check/grab/' + orderId,
    method: 'get'
  })
}

// 获取可分配用户列表
export function getAssignableUsers(orderId) {
  return request({
    url: '/crm/order/assignable/users',
    method: 'get',
    params: { orderId }
  })
}

// 导出订单
export function exportOrder(query) {
  return request({
    url: '/crm/order/export',
    method: 'post',
    data: query
  })
}

// ==================== 客户匹配相关API ====================

// 根据电话号码匹配客户
export function matchCustomerByPhone(phone) {
  return request({
    url: '/crm/customer/matching/phone',
    method: 'get',
    params: { phone }
  })
}

// 根据客户名称匹配客户
export function matchCustomerByName(customerName) {
  return request({
    url: '/crm/customer/matching/name',
    method: 'get',
    params: { customerName }
  })
}

// 根据邮箱匹配客户
export function matchCustomerByEmail(email) {
  return request({
    url: '/crm/customer/matching/email',
    method: 'get',
    params: { email }
  })
}

// 综合匹配客户
export function matchCustomerComprehensive(phone, email, customerName) {
  return request({
    url: '/crm/customer/matching/comprehensive',
    method: 'get',
    params: { phone, email, customerName }
  })
}

// 智能匹配客户
export function smartMatchCustomer(data) {
  return request({
    url: '/crm/customer/matching/smart',
    method: 'post',
    data: data
  })
}

// 检查是否为新客户
export function checkNewCustomer(phone, email, customerName) {
  return request({
    url: '/crm/customer/matching/check/new',
    method: 'get',
    params: { phone, email, customerName }
  })
}

// 客户重复检测
export function checkDuplicateCustomer(data) {
  return request({
    url: '/crm/customer/matching/duplicate/check',
    method: 'post',
    data: data
  })
}

// 获取客户匹配建议
export function getMatchingSuggestion(data) {
  return request({
    url: '/crm/customer/matching/suggestion',
    method: 'post',
    data: data
  })
}

// 批量匹配客户
export function batchMatchCustomer(data) {
  return request({
    url: '/crm/customer/matching/batch',
    method: 'post',
    data: data
  })
}

// 标准化电话号码
export function normalizePhone(phone) {
  return request({
    url: '/crm/customer/matching/normalize/phone',
    method: 'get',
    params: { phone }
  })
}

// 标准化客户名称
export function normalizeCustomerName(customerName) {
  return request({
    url: '/crm/customer/matching/normalize/name',
    method: 'get',
    params: { customerName }
  })
}

// ==================== 新客户通知相关API ====================

// 查询通知列表
export function listNotification(query) {
  return request({
    url: '/crm/notification/list',
    method: 'get',
    params: query
  })
}

// 分页查询通知列表
export function pageNotification(data, pageNum, pageSize) {
  return request({
    url: '/crm/notification/page',
    method: 'post',
    data: data,
    params: { pageNum, pageSize }
  })
}

// 创建新客户通知
export function createNewCustomerNotification(customerId, customerName, customerPhone, orderId, orderNo) {
  return request({
    url: '/crm/notification/create',
    method: 'post',
    params: { customerId, customerName, customerPhone, orderId, orderNo }
  })
}

// 创建未分配订单通知
export function createUnassignedOrderNotification(orderId, orderNo, customerName) {
  return request({
    url: '/crm/notification/create/unassigned',
    method: 'post',
    params: { orderId, orderNo, customerName }
  })
}

// 处理通知
export function processNotification(id, processResult) {
  return request({
    url: '/crm/notification/process/' + id,
    method: 'put',
    params: { processResult }
  })
}

// 批量处理通知
export function batchProcessNotifications(notificationIds, processResult) {
  return request({
    url: '/crm/notification/batch/process',
    method: 'put',
    params: { notificationIds: notificationIds.join(','), processResult }
  })
}

// 分配通知
export function assignNotification(id, assignedTo) {
  return request({
    url: '/crm/notification/assign/' + id,
    method: 'put',
    params: { assignedTo }
  })
}

// 获取待处理通知
export function getPendingNotifications(assignedTo) {
  return request({
    url: '/crm/notification/pending',
    method: 'get',
    params: { assignedTo }
  })
}

// 获取我的待处理通知
export function getMyPendingNotifications() {
  return request({
    url: '/crm/notification/my/pending',
    method: 'get'
  })
}

// 获取超时通知
export function getTimeoutNotifications(timeoutMinutes) {
  return request({
    url: '/crm/notification/timeout',
    method: 'get',
    params: { timeoutMinutes }
  })
}

// 发送企业微信通知
export function sendWechatNotification(id) {
  return request({
    url: '/crm/notification/send/wechat/' + id,
    method: 'post'
  })
}

// 发送邮件通知
export function sendEmailNotification(id) {
  return request({
    url: '/crm/notification/send/email/' + id,
    method: 'post'
  })
}

// 批量发送企业微信通知
export function batchSendWechatNotifications() {
  return request({
    url: '/crm/notification/batch/send/wechat',
    method: 'post'
  })
}

// 批量发送邮件通知
export function batchSendEmailNotifications() {
  return request({
    url: '/crm/notification/batch/send/email',
    method: 'post'
  })
}

// 获取通知统计
export function getNotificationStatistics(assignedTo, startDate, endDate) {
  return request({
    url: '/crm/notification/statistics',
    method: 'get',
    params: { assignedTo, startDate, endDate }
  })
}

// 获取我的通知统计
export function getMyNotificationStatistics(startDate, endDate) {
  return request({
    url: '/crm/notification/my/statistics',
    method: 'get',
    params: { startDate, endDate }
  })
}

// 获取通知处理效率
export function getNotificationEfficiency(startDate, endDate) {
  return request({
    url: '/crm/notification/efficiency',
    method: 'get',
    params: { startDate, endDate }
  })
}

// 清理过期通知
export function cleanExpiredNotifications(expireDays) {
  return request({
    url: '/crm/notification/clean/expired',
    method: 'delete',
    params: { expireDays }
  })
}

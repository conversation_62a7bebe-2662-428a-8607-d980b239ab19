<template>
  <el-dialog 
    title="订单分配" 
    :visible.sync="dialogVisible" 
    width="60%" 
    :before-close="handleClose"
    append-to-body
  >
    <div v-loading="loading">
      <!-- 分配信息 -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分配方式" prop="assignmentType">
              <el-radio-group v-model="form.assignmentType" @change="handleAssignmentTypeChange">
                <el-radio label="manual">手动分配</el-radio>
                <el-radio label="auto">自动分配</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.assignmentType === 'auto'">
            <el-form-item label="分配策略" prop="assignmentStrategy">
              <el-select v-model="form.assignmentStrategy" placeholder="请选择分配策略">
                <el-option label="轮询分配" value="ROUND_ROBIN" />
                <el-option label="工作量均衡" value="WORKLOAD_BASED" />
                <el-option label="客户关系优先" value="CUSTOMER_BASED" />
                <el-option label="技能匹配" value="SKILL_BASED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item 
          label="分配给" 
          prop="toUserId" 
          v-if="form.assignmentType === 'manual'"
        >
          <el-select 
            v-model="form.toUserId" 
            placeholder="请选择分配人员" 
            filterable
            @change="handleUserChange"
          >
            <el-option
              v-for="user in assignableUsers"
              :key="user.userId"
              :label="user.userName + (user.nickName ? `(${user.nickName})` : '')"
              :value="user.userId"
            >
              <span style="float: left">{{ user.userName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ user.deptName }} | 工作量: {{ user.currentWorkload }}/{{ user.maxWorkload }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分配原因" prop="reason">
          <el-input 
            v-model="form.reason" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分配原因"
          />
        </el-form-item>
      </el-form>

      <!-- 待分配订单列表 -->
      <el-card class="mt-3">
        <div slot="header" class="clearfix">
          <span class="card-title">待分配订单 ({{ orderIds.length }}个)</span>
        </div>
        <el-table :data="orderList" border max-height="300">
          <el-table-column label="订单编号" prop="orderNo" width="180" />
          <el-table-column label="客户名称" prop="customerName" width="120" />
          <el-table-column label="客户电话" prop="customerPhone" width="120" />
          <el-table-column label="订单金额" prop="totalAmount" width="100">
            <template slot-scope="scope">
              <span class="text-danger">¥{{ scope.row.totalAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优先级" prop="priorityLevel" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.priorityLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
              <el-tag v-else-if="scope.row.priorityLevel === 'HIGH'" type="warning" size="mini">高</el-tag>
              <el-tag v-else-if="scope.row.priorityLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
              <el-tag v-else type="info" size="mini">低</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 分配人员信息 -->
      <el-card class="mt-3" v-if="selectedUser && form.assignmentType === 'manual'">
        <div slot="header" class="clearfix">
          <span class="card-title">分配人员信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>姓名：</label>
              <span>{{ selectedUser.userName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>部门：</label>
              <span>{{ selectedUser.deptName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>在线状态：</label>
              <el-tag v-if="selectedUser.isOnline" type="success" size="mini">在线</el-tag>
              <el-tag v-else type="info" size="mini">离线</el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>当前工作量：</label>
              <span>{{ selectedUser.currentWorkload }}/{{ selectedUser.maxWorkload }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>工作量比例：</label>
              <el-progress 
                :percentage="selectedUser.workloadRate" 
                :color="getWorkloadColor(selectedUser.workloadRate)"
                :show-text="false"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>今日分配数：</label>
              <span>{{ selectedUser.todayAssignedCount }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 自动分配预览 -->
      <el-card class="mt-3" v-if="form.assignmentType === 'auto' && autoAssignmentPreview.length > 0">
        <div slot="header" class="clearfix">
          <span class="card-title">自动分配预览</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text"
            @click="generateAutoAssignment"
          >重新生成</el-button>
        </div>
        <el-table :data="autoAssignmentPreview" border>
          <el-table-column label="订单编号" prop="orderNo" width="180" />
          <el-table-column label="客户名称" prop="customerName" width="120" />
          <el-table-column label="分配给" prop="assignedUserName" width="120" />
          <el-table-column label="分配原因" prop="assignmentReason" />
        </el-table>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        type="primary" 
        @click="submitAssignment" 
        :loading="submitLoading"
        :disabled="!canSubmit"
      >确认分配</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { assignOrder, batchAssignOrders, getAssignableUsers } from "@/api/crm/order";
import { listOrder } from "@/api/crm/order";

export default {
  name: "OrderAssignmentDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      orderList: [],
      assignableUsers: [],
      selectedUser: null,
      autoAssignmentPreview: [],
      form: {
        assignmentType: 'manual',
        assignmentStrategy: null,
        toUserId: null,
        toUserName: null,
        reason: ''
      },
      rules: {
        assignmentType: [
          { required: true, message: "请选择分配方式", trigger: "change" }
        ],
        toUserId: [
          { required: true, message: "请选择分配人员", trigger: "change" }
        ],
        assignmentStrategy: [
          { required: true, message: "请选择分配策略", trigger: "change" }
        ],
        reason: [
          { required: true, message: "请输入分配原因", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    canSubmit() {
      if (this.form.assignmentType === 'manual') {
        return this.form.toUserId && this.form.reason;
      } else {
        return this.form.assignmentStrategy && this.form.reason && this.autoAssignmentPreview.length > 0;
      }
    }
  },
  watch: {
    orderIds: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && this.visible) {
          this.loadOrderList();
        }
      },
      immediate: true
    },
    visible(newVal) {
      if (newVal) {
        this.loadAssignableUsers();
        if (this.orderIds && this.orderIds.length > 0) {
          this.loadOrderList();
        }
      }
    }
  },
  methods: {
    /** 加载订单列表 */
    loadOrderList() {
      if (!this.orderIds || this.orderIds.length === 0) return;
      
      this.loading = true;
      // TODO: 根据订单ID列表查询订单详情
      // 这里模拟数据
      this.orderList = this.orderIds.map(id => ({
        id: id,
        orderNo: `ORD${id}`,
        customerName: `客户${id}`,
        customerPhone: `138000${id}`,
        totalAmount: (Math.random() * 10000).toFixed(2),
        priorityLevel: ['LOW', 'NORMAL', 'HIGH', 'URGENT'][Math.floor(Math.random() * 4)],
        createTime: new Date()
      }));
      this.loading = false;
    },
    
    /** 加载可分配用户列表 */
    loadAssignableUsers() {
      // TODO: 调用获取可分配用户API
      this.assignableUsers = [
        {
          userId: 1,
          userName: '张三',
          nickName: '销售经理',
          deptName: '销售部',
          isOnline: true,
          currentWorkload: 15,
          maxWorkload: 30,
          workloadRate: 50,
          todayAssignedCount: 3
        },
        {
          userId: 2,
          userName: '李四',
          nickName: '销售专员',
          deptName: '销售部',
          isOnline: false,
          currentWorkload: 20,
          maxWorkload: 25,
          workloadRate: 80,
          todayAssignedCount: 5
        }
      ];
    },
    
    /** 分配方式改变 */
    handleAssignmentTypeChange(value) {
      if (value === 'auto') {
        this.form.toUserId = null;
        this.selectedUser = null;
        this.generateAutoAssignment();
      } else {
        this.autoAssignmentPreview = [];
        this.form.assignmentStrategy = null;
      }
    },
    
    /** 用户选择改变 */
    handleUserChange(userId) {
      this.selectedUser = this.assignableUsers.find(user => user.userId === userId);
      if (this.selectedUser) {
        this.form.toUserName = this.selectedUser.userName;
      }
    },
    
    /** 生成自动分配预览 */
    generateAutoAssignment() {
      if (!this.form.assignmentStrategy) return;
      
      // TODO: 调用自动分配算法API
      this.autoAssignmentPreview = this.orderList.map(order => ({
        ...order,
        assignedUserId: this.assignableUsers[Math.floor(Math.random() * this.assignableUsers.length)].userId,
        assignedUserName: this.assignableUsers[Math.floor(Math.random() * this.assignableUsers.length)].userName,
        assignmentReason: `基于${this.getStrategyName(this.form.assignmentStrategy)}策略自动分配`
      }));
    },
    
    /** 获取策略名称 */
    getStrategyName(strategy) {
      const strategyMap = {
        'ROUND_ROBIN': '轮询分配',
        'WORKLOAD_BASED': '工作量均衡',
        'CUSTOMER_BASED': '客户关系优先',
        'SKILL_BASED': '技能匹配'
      };
      return strategyMap[strategy] || strategy;
    },
    
    /** 获取工作量颜色 */
    getWorkloadColor(percentage) {
      if (percentage < 50) return '#67c23a';
      if (percentage < 80) return '#e6a23c';
      return '#f56c6c';
    },
    
    /** 提交分配 */
    submitAssignment() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          
          if (this.form.assignmentType === 'manual') {
            // 手动分配
            const assignmentData = {
              orderIds: this.orderIds,
              toUserId: this.form.toUserId,
              toUserName: this.form.toUserName,
              reason: this.form.reason
            };
            
            batchAssignOrders(assignmentData).then(() => {
              this.$modal.msgSuccess("分配成功");
              this.handleClose();
              this.$emit('refresh');
            }).finally(() => {
              this.submitLoading = false;
            });
          } else {
            // 自动分配
            // TODO: 实现自动分配逻辑
            this.$modal.msgSuccess("自动分配成功");
            this.handleClose();
            this.$emit('refresh');
            this.submitLoading = false;
          }
        }
      });
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        assignmentType: 'manual',
        assignmentStrategy: null,
        toUserId: null,
        toUserName: null,
        reason: ''
      };
      this.selectedUser = null;
      this.autoAssignmentPreview = [];
      this.orderList = [];
      
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    }
  }
};
</script>

<style scoped>
.card-title {
  font-weight: bold;
  font-size: 16px;
}

.mt-3 {
  margin-top: 20px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
}
</style>

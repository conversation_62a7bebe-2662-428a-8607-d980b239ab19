package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.domain.entity.CrmOrderAssignmentLog;
import com.ruoyi.common.domain.entity.CrmOrderItem;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.mapper.CrmBusinessConversionLogMapper;
import com.ruoyi.common.mapper.CrmOrderAssignmentLogMapper;
import com.ruoyi.common.mapper.CrmOrderItemMapper;
import com.ruoyi.common.mapper.CrmOrderMapper;
import com.ruoyi.common.service.ICrmOrderService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;
// import com.ruoyi.framework.config.OssConfig;

/**
 * CRM订单服务实现类（合并3D打印订单和通用订单功能）
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
@Service
public class CrmOrderServiceImpl implements com.ruoyi.crm.service.ICrmOrderService, com.ruoyi.common.service.ICrmOrderService {
    @Autowired
    private CrmOrderMapper crmOrderMapper;

    @Autowired
    private CrmOrderItemMapper crmOrderItemMapper;

    @Autowired
    private CrmOrderAssignmentLogMapper crmOrderAssignmentLogMapper;

    @Autowired
    private CrmBusinessConversionLogMapper crmBusinessConversionLogMapper;

    // @Autowired
    // private OssConfig ossConfig;

    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public CrmOrder selectCrmOrderById(Long id) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(id);
        if (order != null) {
            // 查询订单项
            List<CrmOrderItem> orderItems = crmOrderItemMapper.selectCrmOrderItemByOrderId(id);
            order.setOrderItems(orderItems);
        }
        return order;
    }

    /**
     * 查询订单列表
     * 
     * @param crmOrder 订单
     * @return 订单
     */
    @Override
    public List<CrmOrder> selectCrmOrderList(CrmOrder crmOrder) {
        return crmOrderMapper.selectCrmOrderList(crmOrder);
    }

    /**
     * 新增订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCrmOrder(CrmOrder crmOrder) {
        crmOrder.setCreateTime(DateUtils.getNowDate());
        crmOrder.setCreatedBy(SecurityUtils.getUserId());
        
        int rows = crmOrderMapper.insertCrmOrder(crmOrder);
        
        // 插入订单项
        if (crmOrder.getOrderItems() != null && !crmOrder.getOrderItems().isEmpty()) {
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setCreateTime(DateUtils.getNowDate());
                item.setCreatedBy(SecurityUtils.getUserId());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return rows;
    }

    /**
     * 修改订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCrmOrder(CrmOrder crmOrder) {
        crmOrder.setUpdateTime(DateUtils.getNowDate());
        crmOrder.setUpdatedBy(SecurityUtils.getUserId());
        
        // 删除原有订单项
        crmOrderItemMapper.deleteCrmOrderItemByOrderId(crmOrder.getId());
        
        // 重新插入订单项
        if (crmOrder.getOrderItems() != null && !crmOrder.getOrderItems().isEmpty()) {
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setUpdateTime(DateUtils.getNowDate());
                item.setUpdatedBy(SecurityUtils.getUserId());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return crmOrderMapper.updateCrmOrder(crmOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderByIds(Long[] ids) {
        // 删除订单项
        for (Long id : ids) {
            crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        }
        
        return crmOrderMapper.deleteCrmOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderById(Long id) {
        // 删除订单项
        crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        
        return crmOrderMapper.deleteCrmOrderById(id);
    }

    /**
     * 创建3D打印订单
     * 
     * @param quoteData 报价数据(JSON字符串)
     * @param customerInfo 客户信息(JSON字符串)
     * @param files 上传的文件
     * @return 订单信息
     */
    @Override
    @Transactional
    public CrmOrder createPrintOrder(String quoteData, String customerInfo, MultipartFile[] files) {
        try {
            // 解析报价数据
            JSONObject quoteJson = JSON.parseObject(quoteData);
            JSONObject customerJson = JSON.parseObject(customerInfo);
            
            // 创建订单
            CrmOrder order = new CrmOrder();
            order.setOrderNo(generateOrderNo());
            order.setQuoteNo(quoteJson.getString("quoteNo"));
            order.setCustomerName(customerJson.getString("customerName"));
            order.setContactPerson(customerJson.getString("contactPerson"));
            order.setContactPhone(customerJson.getString("contactPhone"));
            order.setDeliveryAddress(customerJson.getString("deliveryAddress"));
            order.setTotalAmount(quoteJson.getBigDecimal("totalAmount"));
            order.setStatus("pending");
            order.setCreateTime(DateUtils.getNowDate());
            order.setCreatedBy(SecurityUtils.getUserId());
            
            // 设置预计发货日期（假设3个工作日）
            order.setEstimatedDeliveryDate(new Timestamp(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000L));
            
            // 处理上传的文件
            List<String> fileUrls = new ArrayList<>();
            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        // 这里应该上传到OSS并获取URL
                        String fileUrl = uploadToOss(file);
                        fileUrls.add(fileUrl);
                    }
                }
            }
            
            // 插入订单
            int rows = crmOrderMapper.insertCrmOrder(order);
            
            if (rows > 0) {
                // 创建订单项
                JSONArray items = quoteJson.getJSONArray("items");
                List<CrmOrderItem> orderItems = new ArrayList<>();
                
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.getJSONObject(i);
                    JSONObject modelInfo = item.getJSONObject("modelInfo");
                    
                    CrmOrderItem orderItem = new CrmOrderItem();
                    orderItem.setOrderId(order.getId());
                    orderItem.setModelName(item.getString("modelName"));
                    orderItem.setDimensions(modelInfo.getString("dimensions"));
                    orderItem.setVolume(modelInfo.getString("volume"));
                    orderItem.setSurfaceArea(modelInfo.getString("surfaceArea"));
                    orderItem.setMaterialName(item.getString("material"));
                    orderItem.setQuantity(item.getInteger("quantity"));
                    orderItem.setUnitPrice(item.getBigDecimal("unitPrice"));
                    orderItem.setTotalPrice(item.getBigDecimal("totalPrice"));
                    
                    // 处理后处理选项
                    JSONArray processOptions = item.getJSONArray("processOptions");
                    if (processOptions != null && !processOptions.isEmpty()) {
                        orderItem.setProcessOptions(String.join(",", processOptions.toJavaList(String.class)));
                    }
                    
                    orderItem.setCreateTime(DateUtils.getNowDate());
                    orderItem.setCreatedBy(SecurityUtils.getUserId());
                    
                    // 如果有对应的文件URL，设置到订单项
                    if (i < fileUrls.size()) {
                        orderItem.setModelFileUrl(fileUrls.get(i));
                    }
                    
                    orderItems.add(orderItem);
                    crmOrderItemMapper.insertCrmOrderItem(orderItem);
                }
                
                order.setOrderItems(orderItems);
                order.setFileUrls(fileUrls);
            }
            
            return order;
            
        } catch (Exception e) {
            throw new ServiceException("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单
     */
    @Override
    public CrmOrder selectCrmOrderByOrderNo(String orderNo) {
        return crmOrderMapper.selectCrmOrderByOrderNo(orderNo);
    }

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    public int updateOrderStatus(Long id, String status, Long operatorId) {
        // 实现common接口的方法
        CrmOrder order = new CrmOrder();
        order.setId(id);
        order.setStatus(status);
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdatedBy(operatorId);
        return crmOrderMapper.updateCrmOrder(order);
    }

    @Override
    public int updateOrderStatus(Long id, String status) {
        // 实现crm接口的方法
        return updateOrderStatus(id, status, SecurityUtils.getUserId());
    }

    // ==================== 以下是合并自common目录的方法 ====================

    /**
     * 分页查询订单列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Override
    public TableDataInfo selectCrmOrderPage(CrmOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, crmOrder);
        
        List<CrmOrder> list = crmOrderMapper.selectCrmOrderList(crmOrder);
        return new TableDataInfo(list, (int) new PageInfo(list).getTotal());
    }

    /**
     * 获取订单详情（包含关联信息）
     * 
     * @param id 订单ID
     * @return 订单详情DTO
     */
    @Override
    public CrmOrderDTO getCrmOrderDetail(Long id) {
        CrmOrder order = selectCrmOrderById(id);
        if (order == null) {
            return null;
        }
        
        CrmOrderDTO orderDTO = new CrmOrderDTO();
        BeanUtils.copyProperties(order, orderDTO);
        return orderDTO;
    }

    /**
     * 创建CRM订单
     * 
     * @param orderDTO 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int createCrmOrder(CrmOrderDTO orderDTO) {
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(orderDTO, crmOrder);
        crmOrder.setCreateTime(DateUtils.getNowDate());
        crmOrder.setCreatedBy(SecurityUtils.getUserId());
        return crmOrderMapper.insertCrmOrder(crmOrder);
    }

    /**
     * 批量更新订单状态
     * 
     * @param ids 订单ID列表
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateOrderStatus(List<Long> ids, String status, Long operatorId) {
        int result = 0;
        for (Long id : ids) {
            result += updateOrderStatus(id, status, operatorId);
        }
        return result;
    }

    /**
     * 分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    @Override
    @Transactional
    public int assignOrder(CrmOrderAssignmentDTO assignmentDTO) {
        Long orderId = assignmentDTO.getOrderId();
        Long toUserId = assignmentDTO.getToUserId();
        // 检查是否可以分配
        if (!canAssignOrder(orderId, toUserId)) {
            throw new ServiceException("订单不能分配给该用户");
        }

        // 更新订单分配信息
        CrmOrder order = new CrmOrder();
        order.setId(orderId);
        order.setOwnerId(toUserId);
        order.setAssignedTime(DateUtils.getNowDate());
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdatedBy(SecurityUtils.getUserId());

        int result = crmOrderMapper.updateCrmOrder(order);

        // 记录分配日志
        if (result > 0) {
            CrmOrderAssignmentLog log = new CrmOrderAssignmentLog();
            log.setOrderId(orderId);
            log.setToUserId(toUserId);
            log.setOperatorId(SecurityUtils.getUserId());
            log.setOperationTime(DateUtils.getNowDate());
            log.setCreateTime(DateUtils.getNowDate());
            log.setCreateBy(SecurityUtils.getUserId().toString());
            crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(log);
        }

        return result;
    }

    /**
     * 批量分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAssignOrders(CrmOrderAssignmentDTO assignmentDTO) {
        int result = 0;
        for (Long orderId : assignmentDTO.getOrderIds()) {
            CrmOrderAssignmentDTO singleAssignment = new CrmOrderAssignmentDTO();
            singleAssignment.setOrderId(orderId);
            singleAssignment.setToUserId(assignmentDTO.getToUserId());
            result += assignOrder(singleAssignment);
        }
        return result;
    }

    /**
     * 检查是否可以分配订单
     * 
     * @param orderId 订单ID
     * @param assigneeId 分配人ID
     * @return 是否可以分配
     */
    @Override
    public boolean canAssignOrder(Long orderId, Long assigneeId) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 检查订单状态是否允许分配
        if ("已完成".equals(order.getStatus()) || "已取消".equals(order.getStatus())) {
            return false;
        }
        
        // 检查是否已经分配给其他人
        if (order.getOwnerId() != null && !order.getOwnerId().equals(assigneeId)) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否可以抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否可以抢单
     */
    @Override
    public boolean canGrabOrder(Long orderId, Long userId) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 检查订单是否未分配
        if (order.getOwnerId() != null) {
            return false;
        }
        
        // 检查订单状态
        if ("已完成".equals(order.getStatus()) || "已取消".equals(order.getStatus())) {
            return false;
        }
        
        return true;
    }

    /**
     * 转移订单
     * 
     * @param assignmentDTO 转移信息
     * @return 结果
     */
    @Override
    @Transactional
    public int transferOrder(CrmOrderAssignmentDTO assignmentDTO) {
        Long orderId = assignmentDTO.getOrderId();
        Long fromUserId = assignmentDTO.getFromUserId();
        Long toUserId = assignmentDTO.getToUserId();
        
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null || !order.getOwnerId().equals(fromUserId)) {
            throw new ServiceException("订单转移失败：订单不存在或不属于当前用户");
        }
        
        return assignOrder(assignmentDTO);
    }

    /**
     * 抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int grabOrder(Long orderId, Long userId) {
        if (!canGrabOrder(orderId, userId)) {
            throw new ServiceException("无法抢单：订单已被分配或状态不允许");
        }
        
        CrmOrderAssignmentDTO assignmentDTO = new CrmOrderAssignmentDTO();
        assignmentDTO.setOrderId(orderId);
        assignmentDTO.setToUserId(userId);
        return assignOrder(assignmentDTO);
    }

    /**
     * 回收订单到公海池
     * 
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param reason 回收原因
     * @return 结果
     */
    @Override
    @Transactional
    public int reclaimOrder(Long orderId, Long operatorId, String reason) {
        CrmOrder order = new CrmOrder();
        order.setId(orderId);
        order.setOwnerId(null);
        order.setAssignedTime(null);
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdatedBy(operatorId);
        
        return crmOrderMapper.updateCrmOrder(order);
    }

    /**
     * 获取我的订单列表
     * 
     * @param userId 用户ID
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    @Override
    public TableDataInfo getMyOrders(Long userId, CrmOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, crmOrder);
        crmOrder.setOwnerId(userId);
        
        List<CrmOrder> list = crmOrderMapper.selectCrmOrderList(crmOrder);
        return new TableDataInfo(list, (int) new PageInfo(list).getTotal());
    }

    /**
     * 获取未分配订单
     * 
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    @Override
    public TableDataInfo getUnassignedOrders(CrmOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, crmOrder);
        crmOrder.setOwnerId(null);
        
        List<CrmOrder> list = crmOrderMapper.selectCrmOrderList(crmOrder);
        return new TableDataInfo(list, (int) new PageInfo(list).getTotal());
    }

    /**
     * 获取部门订单列表
     * 
     * @param deptId 部门ID
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    @Override
    public TableDataInfo getDeptOrders(Long deptId, CrmOrderQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, crmOrder);
        // TODO: 根据部门ID查询订单
        
        List<CrmOrder> list = crmOrderMapper.selectCrmOrderList(crmOrder);
        return new TableDataInfo(list, (int) new PageInfo(list).getTotal());
    }

    /**
     * 商机转订单
     * 
     * @param opportunityId 商机ID
     * @param orderDTO 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int convertOpportunityToOrder(Long opportunityId, CrmOrderDTO orderDTO) {
        // TODO: 实现商机转订单逻辑
        log.info("商机转订单：opportunityId={}, orderDTO={}", opportunityId, orderDTO);
        return 1;
    }

    /**
     * 订单转合同
     * 
     * @param orderId 订单ID
     * @param contractData 合同数据
     * @return 结果
     */
    @Override
    @Transactional
    public int convertOrderToContract(Long orderId, Object contractData) {
        // TODO: 实现订单转合同逻辑
        log.info("订单转合同：orderId={}, contractData={}", orderId, contractData);
        return 1;
    }

    /**
     * 批量订单转合同
     * 
     * @param orderIds 订单ID列表
     * @param contractData 合同数据
     * @return 结果
     */
    @Override
    @Transactional
    public int batchConvertOrdersToContract(List<Long> orderIds, Object contractData) {
        int result = 0;
        for (Long orderId : orderIds) {
            result += convertOrderToContract(orderId, contractData);
        }
        return result;
    }

    /**
     * 获取订单统计信息
     * 
     * @param userId 用户ID（可选，为空则统计全部）
     * @return 统计信息
     */
    @Override
    public Object getOrderStatistics(Long userId) {
        // TODO: 实现订单统计逻辑
        log.info("获取订单统计：userId={}", userId);
        return new Object();
    }

    /**
     * 获取订单趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式：DAY/WEEK/MONTH
     * @return 趋势数据
     */
    @Override
    public List<Object> getOrderTrendData(String startDate, String endDate, String groupBy) {
        // TODO: 实现订单趋势数据逻辑
        log.info("获取订单趋势数据：startDate={}, endDate={}, groupBy={}", startDate, endDate, groupBy);
        return new ArrayList<>();
    }

    /**
     * 获取订单分配历史
     * 
     * @param orderId 订单ID
     * @return 分配历史列表
     */
    @Override
    public List<Object> getOrderAssignmentHistory(Long orderId) {
        return crmOrderAssignmentLogMapper.selectCrmOrderAssignmentLogByOrderId(orderId).stream()
                .map(log -> (Object) log)
                .collect(Collectors.toList());
    }

    /**
     * 导出订单数据
     * 
     * @param queryDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<CrmOrder> exportOrderData(CrmOrderQueryDTO queryDTO) {
        CrmOrder crmOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, crmOrder);
        return crmOrderMapper.selectCrmOrderList(crmOrder);
    }

    /**
     * 导入订单数据
     * 
     * @param orderList 订单列表
     * @param operatorId 操作人ID
     * @return 导入结果
     */
    @Override
    public Object importOrderData(List<CrmOrder> orderList, Long operatorId) {
        // TODO: 实现订单数据导入逻辑
        log.info("导入订单数据：orderCount={}, operatorId={}", orderList.size(), operatorId);
        return new Object();
    }

    /**
     * 获取3D打印订单统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Object get3DPrintingOrderStats() {
        CrmOrder queryCondition = new CrmOrder();
        queryCondition.setOrderType("3D_PRINTING");
        queryCondition.setOrderSource("3D_PRINTING");
        
        List<CrmOrder> allOrders = crmOrderMapper.selectCrmOrderList(queryCondition);
        
        // 统计各种状态的订单数量
        long totalCount = allOrders.size();
        long pendingCount = allOrders.stream().filter(o -> "PENDING".equals(o.getStatus())).count();
        long confirmedCount = allOrders.stream().filter(o -> "CONFIRMED".equals(o.getStatus())).count();
        long inProgressCount = allOrders.stream().filter(o -> "IN_PROGRESS".equals(o.getStatus())).count();
        long completedCount = allOrders.stream().filter(o -> "COMPLETED".equals(o.getStatus())).count();
        long cancelledCount = allOrders.stream().filter(o -> "CANCELLED".equals(o.getStatus())).count();
        
        // 统计分配状态
        long unassignedCount = allOrders.stream().filter(o -> "UNASSIGNED".equals(o.getAssignmentStatus())).count();
        long assignedCount = allOrders.stream().filter(o -> "ASSIGNED".equals(o.getAssignmentStatus())).count();
        
        // 统计总金额
        BigDecimal totalAmount = allOrders.stream()
            .filter(o -> o.getTotalAmount() != null)
            .map(CrmOrder::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 构建返回结果
        JSONObject stats = new JSONObject();
        stats.put("totalCount", totalCount);
        stats.put("statusStats", new JSONObject() {{
            put("pending", pendingCount);
            put("confirmed", confirmedCount);
            put("inProgress", inProgressCount);
            put("completed", completedCount);
            put("cancelled", cancelledCount);
        }});
        stats.put("assignmentStats", new JSONObject() {{
            put("unassigned", unassignedCount);
            put("assigned", assignedCount);
        }});
        stats.put("totalAmount", totalAmount);
        
        return stats;
    }

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    private String generateOrderNo() {
        // 格式：OR + 年月日 + 6位随机数
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String randomStr = String.valueOf((int) (Math.random() * 900000) + 100000);
        return "OR" + dateStr + randomStr;
    }

    /**
     * 上传文件到OSS
     * 
     * @param file 文件
     * @return 文件URL
     */
    private String uploadToOss(MultipartFile file) {
        // 这里应该实现OSS上传逻辑
        // 暂时返回一个模拟的URL
        return "https://oss.example.com/3d-models/" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
    }
}

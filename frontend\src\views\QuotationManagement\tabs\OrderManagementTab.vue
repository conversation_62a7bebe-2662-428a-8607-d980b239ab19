<template>
    <div class="order-management-tab">
        <div class="tab-header">
            <h3>3D打印订单管理</h3>
            <div class="header-stats">
                <el-space wrap>
                    <el-tag size="large">总订单: {{ stats.totalCount || 0 }}</el-tag>
                    <el-tag type="warning" size="large">待处理: {{ stats.statusStats?.pending || 0 }}</el-tag>
                    <el-tag type="success" size="large">进行中: {{ stats.statusStats?.inProgress || 0 }}</el-tag>
                    <el-tag type="info" size="large">已完成: {{ stats.statusStats?.completed || 0 }}</el-tag>
                </el-space>
            </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="filter-section">
            <common-filter
                v-model:searchValue="searchInput"
                v-model:filterValue="filterType"
                :config="filterConfig"
                @search="handleSearch"
                @filter="handleFilterChange"
            />
        </div>

        <!-- 订单列表表格 -->
        <el-table 
            :data="orders" 
            v-loading="loading"
            border 
            sortable 
            tooltip-effect="dark"
            :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
            style="width: 100%; margin-top: 16px; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
            height="400">
            
            <el-table-column prop="orderNo" label="订单编号" min-width="140">
                <template #default="scope">
                    <el-button link type="primary" @click="viewOrderDetail(scope.row)">
                        {{ scope.row.orderNo }}
                    </el-button>
                </template>
            </el-table-column>
            
            <el-table-column prop="customerName" label="客户名称" min-width="150" />
            <el-table-column prop="contactPerson" label="联系人" min-width="120" />
            <el-table-column prop="itemCount" label="模型数量" min-width="100" align="center" />
            
            <el-table-column prop="totalAmount" label="订单金额" min-width="120" align="right">
                <template #default="scope">
                    <span style="color: #e6a23c; font-weight: 500;">
                        {{ scope.row.currency || 'CNY' }} {{ formatCurrency(scope.row.totalAmount) }}
                    </span>
                </template>
            </el-table-column>
            
            <el-table-column prop="status" label="订单状态" min-width="100" align="center">
                <template #default="scope">
                    <el-tag 
                        :type="getStatusTagType(scope.row.status)" 
                        size="small">
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="assignmentStatus" label="分配状态" min-width="100" align="center">
                <template #default="scope">
                    <el-tag 
                        :type="scope.row.assignmentStatus === 'ASSIGNED' ? 'success' : 'warning'" 
                        size="small">
                        {{ scope.row.assignmentStatus === 'ASSIGNED' ? '已分配' : '未分配' }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="ownerName" label="负责人" min-width="120" />
            <el-table-column prop="orderDate" label="下单日期" min-width="120" />
            
            <el-table-column label="操作" width="180" fixed="right">
                <template #default="scope">
                    <el-button 
                        type="primary" 
                        link 
                        size="small" 
                        @click="viewOrderDetail(scope.row)">
                        <el-icon><View /></el-icon>
                        查看详情
                    </el-button>
                    <el-button 
                        type="success" 
                        link 
                        size="small" 
                        @click="editOrderStatus(scope.row)">
                        <el-icon><Edit /></el-icon>
                        更新状态
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <pagination 
            v-show="total > 0" 
            :total="total" 
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handlePagination" 
        />

        <!-- 订单详情抽屉 -->
        <el-drawer
            v-model="detailDrawerVisible"
            title="订单详情"
            size="60%"
            direction="rtl">
            <order-detail-drawer 
                v-if="detailDrawerVisible"
                :order-id="selectedOrderId" 
                @close="detailDrawerVisible = false" />
        </el-drawer>

        <!-- 状态更新对话框 -->
        <el-dialog v-model="statusDialogVisible" title="更新订单状态" width="400px">
            <el-form :model="statusForm" label-width="80px">
                <el-form-item label="当前状态">
                    <el-tag>{{ getStatusText(statusForm.currentStatus) }}</el-tag>
                </el-form-item>
                <el-form-item label="新状态" required>
                    <el-select v-model="statusForm.newStatus" placeholder="请选择新状态">
                        <el-option label="待处理" value="PENDING" />
                        <el-option label="已确认" value="CONFIRMED" />
                        <el-option label="进行中" value="IN_PROGRESS" />
                        <el-option label="已完成" value="COMPLETED" />
                        <el-option label="已取消" value="CANCELLED" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input 
                        v-model="statusForm.remark" 
                        type="textarea" 
                        placeholder="请输入状态更新备注"
                        :rows="3" />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="statusDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleUpdateStatus" :loading="statusUpdateLoading">
                        确定更新
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Edit } from '@element-plus/icons-vue'

import CommonFilter from '@/components/CommonFilter/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import OrderDetailDrawer from '../components/OrderDetailDrawer.vue'
import { get3DPrintingOrders, get3DPrintingOrderStats, updateOrderStatus } from '@/api/crm/order'

// 状态管理
const loading = ref(false)
const statusUpdateLoading = ref(false)

// 数据
const orders = ref([])
const stats = ref({})
const total = ref(0)

// UI状态
const detailDrawerVisible = ref(false)
const statusDialogVisible = ref(false)
const selectedOrderId = ref(null)

// 搜索和筛选
const searchInput = ref('')
const filterType = ref('all')

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    orderNo: '',
    customerName: '',
    status: '',
    assignmentStatus: '',
    orderType: '3D_PRINTING',
    orderSource: '3D_PRINTING'
})

// 状态更新表单
const statusForm = reactive({
    orderId: null,
    currentStatus: '',
    newStatus: '',
    remark: ''
})

// 筛选配置
const filterConfig = {
    search: {
        placeholder: '搜索订单号、客户名称',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '订单状态：',
        options: [
            { label: '全部', value: 'all' },
            { label: '待处理', value: 'PENDING' },
            { label: '已确认', value: 'CONFIRMED' },
            { label: '进行中', value: 'IN_PROGRESS' },
            { label: '已完成', value: 'COMPLETED' },
            { label: '已取消', value: 'CANCELLED' }
        ],
        buttonStyle: true,
        size: 'default' as const
    }
}

// 生命周期
onMounted(() => {
    loadOrders()
    loadStats()
})

// 方法
const loadOrders = async () => {
    loading.value = true
    try {
        const response = await get3DPrintingOrders(queryParams)
        if (response && 'code' in response && response.code === 200) {
            orders.value = (response as any).rows || []
            total.value = (response as any).total || 0
        }
    } catch (error) {
        ElMessage.error('加载订单列表失败')
    } finally {
        loading.value = false
    }
}

const loadStats = async () => {
    try {
        const response = await get3DPrintingOrderStats()
        if (response && 'code' in response && response.code === 200) {
            stats.value = (response as any).data || {}
        }
    } catch (error) {
        console.error('加载统计信息失败:', error)
    }
}

const viewOrderDetail = (order: any) => {
    selectedOrderId.value = order.id
    detailDrawerVisible.value = true
}

const editOrderStatus = (order: any) => {
    statusForm.orderId = order.id
    statusForm.currentStatus = order.status
    statusForm.newStatus = ''
    statusForm.remark = ''
    statusDialogVisible.value = true
}

const handleUpdateStatus = async () => {
    if (!statusForm.newStatus) {
        ElMessage.warning('请选择新状态')
        return
    }
    
    if (!statusForm.orderId) {
        ElMessage.error('订单ID无效')
        return
    }
    
    statusUpdateLoading.value = true
    try {
        const response = await updateOrderStatus(statusForm.orderId, {
            status: statusForm.newStatus,
            remark: statusForm.remark
        })
        if (response && 'code' in response && response.code === 200) {
            ElMessage.success('状态更新成功')
            statusDialogVisible.value = false
            loadOrders()
            loadStats()
        } else {
            ElMessage.error((response as any)?.msg || '状态更新失败')
        }
    } catch (error) {
        ElMessage.error('状态更新失败')
    } finally {
        statusUpdateLoading.value = false
    }
}

const handleSearch = (value: string) => {
    queryParams.orderNo = value
    queryParams.customerName = value
    queryParams.pageNum = 1
    loadOrders()
}

const handleFilterChange = (value: string) => {
    queryParams.status = value === 'all' ? '' : value
    queryParams.pageNum = 1
    loadOrders()
}

const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page
    queryParams.pageSize = val.limit
    loadOrders()
}

// 辅助函数
const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return isNaN(num) ? '0.00' : num.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        'PENDING': '待处理',
        'CONFIRMED': '已确认',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
    }
    return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
    const typeMap: Record<string, string> = {
        'PENDING': 'warning',
        'CONFIRMED': 'primary',
        'IN_PROGRESS': 'success',
        'COMPLETED': 'success',
        'CANCELLED': 'danger'
    }
    return typeMap[status] || 'info'
}

</script>

<style scoped>
.order-management-tab {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

.header-stats {
    display: flex;
    align-items: center;
}

.filter-section {
    margin-bottom: 16px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
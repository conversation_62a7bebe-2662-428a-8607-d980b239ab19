<template>
  <el-dialog 
    :title="title" 
    :visible.sync="dialogVisible" 
    width="70%" 
    :before-close="handleClose"
    append-to-body
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <!-- 基本信息 -->
      <el-card class="mb-3">
        <div slot="header" class="clearfix">
          <span class="card-title">基本信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单编号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请输入订单编号" :disabled="isEdit" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择订单状态">
                <el-option label="待处理" value="PENDING" />
                <el-option label="处理中" value="PROCESSING" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户电话" prop="customerPhone">
              <el-input v-model="form.customerPhone" placeholder="请输入客户电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户邮箱" prop="customerEmail">
              <el-input v-model="form.customerEmail" placeholder="请输入客户邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户地址" prop="customerAddress">
              <el-input v-model="form.customerAddress" placeholder="请输入客户地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单来源" prop="orderSource">
              <el-select v-model="form.orderSource" placeholder="请选择订单来源">
                <el-option label="3D打印" value="3D_PRINTING" />
                <el-option label="手动录入" value="MANUAL" />
                <el-option label="在线下单" value="ONLINE" />
                <el-option label="电话下单" value="PHONE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="form.orderType" placeholder="请选择订单类型">
                <el-option label="3D打印" value="3D_PRINTING" />
                <el-option label="标准订单" value="STANDARD" />
                <el-option label="定制订单" value="CUSTOM" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priorityLevel">
              <el-select v-model="form.priorityLevel" placeholder="请选择优先级">
                <el-option label="低" value="LOW" />
                <el-option label="普通" value="NORMAL" />
                <el-option label="高" value="HIGH" />
                <el-option label="紧急" value="URGENT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单日期" prop="orderDate">
              <el-date-picker
                v-model="form.orderDate"
                type="datetime"
                placeholder="选择订单日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input 
                v-model="form.remarks" 
                type="textarea" 
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 订单明细 -->
      <el-card>
        <div slot="header" class="clearfix">
          <span class="card-title">订单明细</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text"
            @click="addOrderItem"
          >添加明细</el-button>
        </div>
        <el-table :data="form.orderItems" border>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="产品名称" align="center" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.productName" placeholder="产品名称" size="mini" />
            </template>
          </el-table-column>
          <el-table-column label="产品编码" align="center" width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.productCode" placeholder="产品编码" size="mini" />
            </template>
          </el-table-column>
          <el-table-column label="规格型号" align="center" width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.specification" placeholder="规格型号" size="mini" />
            </template>
          </el-table-column>
          <el-table-column label="数量" align="center" width="80">
            <template slot-scope="scope">
              <el-input-number 
                v-model="scope.row.quantity" 
                :min="1" 
                size="mini"
                @change="calculateSubtotal(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" width="80">
            <template slot-scope="scope">
              <el-input v-model="scope.row.unit" placeholder="单位" size="mini" />
            </template>
          </el-table-column>
          <el-table-column label="单价" align="center" width="100">
            <template slot-scope="scope">
              <el-input-number 
                v-model="scope.row.unitPrice" 
                :precision="2" 
                :min="0" 
                size="mini"
                @change="calculateSubtotal(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="折扣率%" align="center" width="80">
            <template slot-scope="scope">
              <el-input-number 
                v-model="scope.row.discountRate" 
                :precision="2" 
                :min="0" 
                :max="100"
                size="mini"
                @change="calculateSubtotal(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="小计" align="center" width="100">
            <template slot-scope="scope">
              <span class="text-danger">¥{{ scope.row.subtotal || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remarks" placeholder="备注" size="mini" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="text" 
                icon="el-icon-delete"
                @click="removeOrderItem(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 金额汇总 -->
        <div class="amount-summary">
          <el-row :gutter="20">
            <el-col :span="16"></el-col>
            <el-col :span="8">
              <el-form-item label="商品总额：">
                <el-input-number 
                  v-model="form.subtotalAmount" 
                  :precision="2" 
                  :min="0" 
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="优惠金额：">
                <el-input-number 
                  v-model="form.discountAmount" 
                  :precision="2" 
                  :min="0" 
                  style="width: 100%"
                  @change="calculateTotalAmount"
                />
              </el-form-item>
              <el-form-item label="运费：">
                <el-input-number 
                  v-model="form.shippingFee" 
                  :precision="2" 
                  :min="0" 
                  style="width: 100%"
                  @change="calculateTotalAmount"
                />
              </el-form-item>
              <el-form-item label="订单总额：" class="total-amount">
                <el-input-number 
                  v-model="form.totalAmount" 
                  :precision="2" 
                  :min="0" 
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addOrder, updateOrder } from "@/api/crm/order";

export default {
  name: "OrderEditDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    order: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitLoading: false,
      form: {
        id: null,
        orderNo: null,
        customerName: null,
        customerPhone: null,
        customerEmail: null,
        customerAddress: null,
        orderSource: null,
        orderType: null,
        status: "PENDING",
        priorityLevel: "NORMAL",
        orderDate: null,
        subtotalAmount: 0,
        discountAmount: 0,
        shippingFee: 0,
        totalAmount: 0,
        remarks: null,
        orderItems: []
      },
      rules: {
        orderNo: [
          { required: true, message: "订单编号不能为空", trigger: "blur" }
        ],
        customerName: [
          { required: true, message: "客户名称不能为空", trigger: "blur" }
        ],
        customerPhone: [
          { required: true, message: "客户电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        orderSource: [
          { required: true, message: "订单来源不能为空", trigger: "change" }
        ],
        orderType: [
          { required: true, message: "订单类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    title() {
      return this.isEdit ? "修改订单" : "新增订单";
    },
    isEdit() {
      return this.form.id != null;
    }
  },
  watch: {
    order: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.form = { ...newVal };
          if (!this.form.orderItems) {
            this.form.orderItems = [];
          }
          this.calculateSubtotalAmount();
        } else {
          this.resetForm();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /** 添加订单明细 */
    addOrderItem() {
      this.form.orderItems.push({
        productName: '',
        productCode: '',
        specification: '',
        quantity: 1,
        unit: '个',
        unitPrice: 0,
        discountRate: 0,
        subtotal: 0,
        remarks: ''
      });
    },
    
    /** 删除订单明细 */
    removeOrderItem(index) {
      this.form.orderItems.splice(index, 1);
      this.calculateSubtotalAmount();
    },
    
    /** 计算明细小计 */
    calculateSubtotal(item) {
      const quantity = item.quantity || 0;
      const unitPrice = item.unitPrice || 0;
      const discountRate = item.discountRate || 0;
      
      item.subtotal = (quantity * unitPrice * (100 - discountRate) / 100).toFixed(2);
      this.calculateSubtotalAmount();
    },
    
    /** 计算商品总额 */
    calculateSubtotalAmount() {
      let subtotal = 0;
      this.form.orderItems.forEach(item => {
        subtotal += parseFloat(item.subtotal || 0);
      });
      this.form.subtotalAmount = subtotal.toFixed(2);
      this.calculateTotalAmount();
    },
    
    /** 计算订单总额 */
    calculateTotalAmount() {
      const subtotal = parseFloat(this.form.subtotalAmount || 0);
      const discount = parseFloat(this.form.discountAmount || 0);
      const shipping = parseFloat(this.form.shippingFee || 0);
      
      this.form.totalAmount = (subtotal - discount + shipping).toFixed(2);
    },
    
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          
          // 生成订单编号
          if (!this.isEdit && !this.form.orderNo) {
            this.form.orderNo = 'ORD' + new Date().getTime();
          }
          
          // 设置订单日期
          if (!this.form.orderDate) {
            this.form.orderDate = new Date();
          }
          
          const submitData = { ...this.form };
          
          const submitPromise = this.isEdit 
            ? updateOrder(submitData) 
            : addOrder(submitData);
            
          submitPromise.then(() => {
            this.$modal.msgSuccess(this.isEdit ? "修改成功" : "新增成功");
            this.handleClose();
            this.$emit('refresh');
          }).finally(() => {
            this.submitLoading = false;
          });
        }
      });
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        orderNo: null,
        customerName: null,
        customerPhone: null,
        customerEmail: null,
        customerAddress: null,
        orderSource: null,
        orderType: null,
        status: "PENDING",
        priorityLevel: "NORMAL",
        orderDate: null,
        subtotalAmount: 0,
        discountAmount: 0,
        shippingFee: 0,
        totalAmount: 0,
        remarks: null,
        orderItems: []
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    }
  }
};
</script>

<style scoped>
.card-title {
  font-weight: bold;
  font-size: 16px;
}

.mb-3 {
  margin-bottom: 20px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.amount-summary {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.total-amount {
  font-weight: bold;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}
</style>

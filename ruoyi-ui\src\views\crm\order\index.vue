<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户电话" prop="customerPhone">
        <el-input
          v-model="queryParams.customerPhone"
          placeholder="请输入客户电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单来源" prop="orderSource">
        <el-select v-model="queryParams.orderSource" placeholder="请选择订单来源" clearable>
          <el-option label="3D打印" value="3D_PRINTING" />
          <el-option label="手动录入" value="MANUAL" />
          <el-option label="在线下单" value="ONLINE" />
          <el-option label="电话下单" value="PHONE" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="处理中" value="PROCESSING" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item label="分配状态" prop="assignmentStatus">
        <el-select v-model="queryParams.assignmentStatus" placeholder="请选择分配状态" clearable>
          <el-option label="未分配" value="UNASSIGNED" />
          <el-option label="已分配" value="ASSIGNED" />
          <el-option label="已转移" value="TRANSFERRED" />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priorityLevel">
        <el-select v-model="queryParams.priorityLevel" placeholder="请选择优先级" clearable>
          <el-option label="低" value="LOW" />
          <el-option label="普通" value="NORMAL" />
          <el-option label="高" value="HIGH" />
          <el-option label="紧急" value="URGENT" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['crm:order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['crm:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['crm:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['crm:order:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-operation"
          size="mini"
          @click="handleAssignment"
          :disabled="multiple"
          v-hasPermi="['crm:order:assign']"
        >批量分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-view"
          size="mini"
          @click="showUnassigned = true"
          v-hasPermi="['crm:order:list']"
        >未分配订单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" align="center" prop="orderNo" width="180">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleView(scope.row)">{{ scope.row.orderNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="客户信息" align="center" width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.customerName }}</div>
          <div class="text-muted">{{ scope.row.customerPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="totalAmount" width="120">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单来源" align="center" prop="orderSource" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.crm_order_source" :value="scope.row.orderSource"/>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.crm_order_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="分配状态" align="center" prop="assignmentStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.assignmentStatus === 'UNASSIGNED'" type="info">未分配</el-tag>
          <el-tag v-else-if="scope.row.assignmentStatus === 'ASSIGNED'" type="success">已分配</el-tag>
          <el-tag v-else-if="scope.row.assignmentStatus === 'TRANSFERRED'" type="warning">已转移</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="ownerName" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.ownerName">{{ scope.row.ownerName }}</span>
          <span v-else class="text-muted">未分配</span>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priorityLevel" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.priorityLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
          <el-tag v-else-if="scope.row.priorityLevel === 'HIGH'" type="warning" size="mini">高</el-tag>
          <el-tag v-else-if="scope.row.priorityLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
          <el-tag v-else type="info" size="mini">低</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['crm:order:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['crm:order:edit']"
          >修改</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['crm:order:assign']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="assign" icon="el-icon-s-custom">分配</el-dropdown-item>
              <el-dropdown-item command="transfer" icon="el-icon-sort">转移</el-dropdown-item>
              <el-dropdown-item command="grab" icon="el-icon-download" v-if="scope.row.assignmentStatus === 'UNASSIGNED'">抢单</el-dropdown-item>
              <el-dropdown-item command="reclaim" icon="el-icon-refresh-left">回收</el-dropdown-item>
              <el-dropdown-item command="history" icon="el-icon-time">分配历史</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <order-detail-dialog 
      :visible.sync="detailVisible" 
      :order-id="currentOrderId"
      @refresh="getList"
    />

    <!-- 订单编辑对话框 -->
    <order-edit-dialog 
      :visible.sync="editVisible" 
      :order="currentOrder"
      @refresh="getList"
    />

    <!-- 订单分配对话框 -->
    <order-assignment-dialog 
      :visible.sync="assignmentVisible" 
      :order-ids="selectedOrderIds"
      @refresh="getList"
    />

    <!-- 未分配订单对话框 -->
    <unassigned-orders-dialog 
      :visible.sync="showUnassigned"
      @refresh="getList"
    />
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, exportOrder } from "@/api/crm/order";
import OrderDetailDialog from "./components/OrderDetailDialog";
import OrderEditDialog from "./components/OrderEditDialog";
import OrderAssignmentDialog from "./components/OrderAssignmentDialog";
import UnassignedOrdersDialog from "./components/UnassignedOrdersDialog";

export default {
  name: "Order",
  dicts: ['crm_order_source', 'crm_order_status'],
  components: {
    OrderDetailDialog,
    OrderEditDialog,
    OrderAssignmentDialog,
    UnassignedOrdersDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        customerName: null,
        customerPhone: null,
        orderSource: null,
        status: null,
        assignmentStatus: null,
        priorityLevel: null,
      },
      // 对话框显示状态
      detailVisible: false,
      editVisible: false,
      assignmentVisible: false,
      showUnassigned: false,
      // 当前操作的订单
      currentOrder: {},
      currentOrderId: null,
      selectedOrderIds: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrder(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderNo: null,
        customerName: null,
        customerPhone: null,
        totalAmount: null,
        orderSource: null,
        status: "PENDING",
        assignmentStatus: "UNASSIGNED",
        priorityLevel: "NORMAL"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.selectedOrderIds = this.ids;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.currentOrder = {};
      this.editVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids[0];
      getOrder(id).then(response => {
        this.currentOrder = response.data;
        this.editVisible = true;
      });
    },
    /** 查看详情 */
    handleView(row) {
      this.currentOrderId = row.id;
      this.detailVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除订单编号为"' + ids + '"的数据项？').then(function() {
        return delOrder(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('crm/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    },
    /** 批量分配 */
    handleAssignment() {
      if (this.selectedOrderIds.length === 0) {
        this.$modal.msgError("请选择要分配的订单");
        return;
      }
      this.assignmentVisible = true;
    },
    /** 下拉菜单操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'assign':
          this.selectedOrderIds = [row.id];
          this.assignmentVisible = true;
          break;
        case 'transfer':
          this.handleTransfer(row);
          break;
        case 'grab':
          this.handleGrab(row);
          break;
        case 'reclaim':
          this.handleReclaim(row);
          break;
        case 'history':
          this.handleHistory(row);
          break;
      }
    },
    /** 转移订单 */
    handleTransfer(row) {
      // TODO: 实现转移功能
      this.$modal.msgInfo("转移功能开发中");
    },
    /** 抢单 */
    handleGrab(row) {
      // TODO: 实现抢单功能
      this.$modal.msgInfo("抢单功能开发中");
    },
    /** 回收订单 */
    handleReclaim(row) {
      // TODO: 实现回收功能
      this.$modal.msgInfo("回收功能开发中");
    },
    /** 查看分配历史 */
    handleHistory(row) {
      // TODO: 实现分配历史查看
      this.$modal.msgInfo("分配历史功能开发中");
    }
  }
};
</script>

<style scoped>
.text-muted {
  color: #999;
  font-size: 12px;
}
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style>

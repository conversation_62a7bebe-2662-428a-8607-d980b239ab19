package com.ruoyi.crm.controller;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * 订单管理模块集成测试
 * 测试报价管理模块中的订单管理子功能
 */
@SpringBootTest
@AutoConfigureMockMvc
@TestPropertySource(locations = "classpath:application-test.properties")
class OrderManagementIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    /**
     * 测试获取3D打印订单列表
     */
    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGet3DPrintingOrdersList() throws Exception {
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setOrderType("3D_PRINTING");

        mockMvc.perform(MockMvcRequestBuilders
                .post("/crm/order/quote/3d-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(queryDTO)))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试获取3D打印订单统计信息
     */
    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGet3DPrintingOrderStats() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                .get("/crm/order/quote/3d-orders/stats"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").exists());
    }

    /**
     * 测试获取3D打印订单详情（模拟数据）
     */
    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGet3DPrintingOrderDetail() throws Exception {
        // 使用一个假的订单ID进行测试，应该返回404或相应错误
        mockMvc.perform(MockMvcRequestBuilders
                .get("/crm/order/quote/3d-order/999999"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
        // 注意：这里可能返回空结果或错误信息，这是正常的
    }

    /**
     * 测试订单状态更新
     */
    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testUpdateOrderStatus() throws Exception {
        // 使用一个假的订单ID测试状态更新接口
        mockMvc.perform(MockMvcRequestBuilders
                .put("/crm/order/status/999999")
                .param("status", "CONFIRMED")
                .param("remark", "测试状态更新"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
        // 注意：这里可能会因为订单不存在而返回错误，这是正常的
    }

    /**
     * 测试未认证访问（应该被拒绝）
     */
    @Test
    void testUnauthorizedAccess() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                .get("/crm/order/quote/3d-orders/stats"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }
}
package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrder;

/**
 * CRM订单服务接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface ICrmOrderService {
    
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public CrmOrder selectCrmOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param crmOrder 订单
     * @return 订单集合
     */
    public List<CrmOrder> selectCrmOrderList(CrmOrder crmOrder);

    /**
     * 分页查询订单列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public TableDataInfo selectCrmOrderPage(CrmOrderQueryDTO queryDTO);

    /**
     * 新增订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    public int insertCrmOrder(CrmOrder crmOrder);

    /**
     * 修改订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    public int updateCrmOrder(CrmOrder crmOrder);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteCrmOrderByIds(Long[] ids);

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteCrmOrderById(Long id);

    /**
     * 获取订单详情（包含关联信息）
     * 
     * @param id 订单ID
     * @return 订单详情DTO
     */
    public CrmOrderDTO getCrmOrderDetail(Long id);

    /**
     * 创建订单（包含订单项）
     * 
     * @param orderDTO 订单DTO
     * @return 结果
     */
    public int createCrmOrder(CrmOrderDTO orderDTO);

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    public int updateOrderStatus(Long id, String status, Long operatorId);

    /**
     * 批量更新订单状态
     * 
     * @param ids 订单ID列表
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    public int batchUpdateOrderStatus(List<Long> ids, String status, Long operatorId);

    /**
     * 分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    public int assignOrder(CrmOrderAssignmentDTO assignmentDTO);

    /**
     * 批量分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    public int batchAssignOrders(CrmOrderAssignmentDTO assignmentDTO);

    /**
     * 转移订单
     * 
     * @param assignmentDTO 转移信息
     * @return 结果
     */
    public int transferOrder(CrmOrderAssignmentDTO assignmentDTO);

    /**
     * 抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    public int grabOrder(Long orderId, Long userId);

    /**
     * 回收订单到公海池
     * 
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param reason 回收原因
     * @return 结果
     */
    public int reclaimOrder(Long orderId, Long operatorId, String reason);

    /**
     * 获取我的订单列表
     * 
     * @param userId 用户ID
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    public TableDataInfo getMyOrders(Long userId, CrmOrderQueryDTO queryDTO);

    /**
     * 获取未分配订单列表
     * 
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    public TableDataInfo getUnassignedOrders(CrmOrderQueryDTO queryDTO);

    /**
     * 获取部门订单列表
     * 
     * @param deptId 部门ID
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    public TableDataInfo getDeptOrders(Long deptId, CrmOrderQueryDTO queryDTO);

    /**
     * 商机转订单
     * 
     * @param opportunityId 商机ID
     * @param orderDTO 订单信息
     * @return 结果
     */
    public int convertOpportunityToOrder(Long opportunityId, CrmOrderDTO orderDTO);

    /**
     * 订单转合同
     * 
     * @param orderId 订单ID
     * @param contractData 合同数据
     * @return 结果
     */
    public int convertOrderToContract(Long orderId, Object contractData);

    /**
     * 批量订单转合同
     * 
     * @param orderIds 订单ID列表
     * @param contractData 合同数据
     * @return 结果
     */
    public int batchConvertOrdersToContract(List<Long> orderIds, Object contractData);

    /**
     * 获取订单统计信息
     * 
     * @param userId 用户ID（可选，为空则统计全部）
     * @return 统计信息
     */
    public Object getOrderStatistics(Long userId);

    /**
     * 获取订单趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式：DAY/WEEK/MONTH
     * @return 趋势数据
     */
    public List<Object> getOrderTrendData(String startDate, String endDate, String groupBy);

    /**
     * 检查订单是否可以分配
     * 
     * @param orderId 订单ID
     * @param userId 目标用户ID
     * @return 检查结果
     */
    public boolean canAssignOrder(Long orderId, Long userId);

    /**
     * 检查用户是否可以抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 检查结果
     */
    public boolean canGrabOrder(Long orderId, Long userId);

    /**
     * 获取订单分配历史
     * 
     * @param orderId 订单ID
     * @return 分配历史列表
     */
    public List<Object> getOrderAssignmentHistory(Long orderId);

    /**
     * 导出订单数据
     * 
     * @param queryDTO 查询条件
     * @return 导出数据
     */
    public List<CrmOrder> exportOrderData(CrmOrderQueryDTO queryDTO);

    /**
     * 导入订单数据
     * 
     * @param orderList 订单列表
     * @param operatorId 操作人ID
     * @return 导入结果
     */
    public Object importOrderData(List<CrmOrder> orderList, Long operatorId);

    /**
     * 获取3D打印订单统计信息
     * 
     * @return 统计信息
     */
    public Object get3DPrintingOrderStats();
}

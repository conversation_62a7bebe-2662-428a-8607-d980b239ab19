<template>
  <el-dialog 
    title="未分配订单" 
    :visible.sync="dialogVisible" 
    width="80%" 
    :before-close="handleClose"
    append-to-body
  >
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优先级" prop="priorityLevel">
        <el-select v-model="queryParams.priorityLevel" placeholder="请选择优先级" clearable style="width: 120px">
          <el-option label="紧急" value="URGENT" />
          <el-option label="高" value="HIGH" />
          <el-option label="普通" value="NORMAL" />
          <el-option label="低" value="LOW" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-operation"
          size="mini"
          @click="handleBatchAssign"
          :disabled="!hasSelection"
        >批量分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleBatchGrab"
          :disabled="!hasSelection"
        >批量抢单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleAutoAssign"
        >智能分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="handleSort"
        >优先级排序</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="orderList" 
      @selection-change="handleSelectionChange"
      :row-class-name="getRowClassName"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" align="center" prop="orderNo" width="180">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleView(scope.row)">{{ scope.row.orderNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="客户信息" align="center" width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.customerName }}</div>
          <div class="text-muted">{{ scope.row.customerPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="totalAmount" width="120">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单来源" align="center" prop="orderSource" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.crm_order_source" :value="scope.row.orderSource"/>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priorityLevel" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.priorityLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
          <el-tag v-else-if="scope.row.priorityLevel === 'HIGH'" type="warning" size="mini">高</el-tag>
          <el-tag v-else-if="scope.row.priorityLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
          <el-tag v-else type="info" size="mini">低</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="等待时间" align="center" width="120">
        <template slot-scope="scope">
          <span :class="getWaitTimeClass(scope.row.waitTime)">{{ scope.row.waitTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isNewCustomer" type="success" size="mini">新客户</el-tag>
          <el-tag v-else type="info" size="mini">老客户</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-custom"
            @click="handleAssign(scope.row)"
          >分配</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleGrab(scope.row)"
            :disabled="!canGrab(scope.row)"
          >抢单</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 统计信息 -->
    <el-card class="mt-3">
      <div slot="header" class="clearfix">
        <span class="card-title">统计信息</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-danger">{{ statistics.totalCount }}</div>
            <div class="stat-label">未分配订单总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-warning">{{ statistics.urgentCount }}</div>
            <div class="stat-label">紧急订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-success">{{ statistics.newCustomerCount }}</div>
            <div class="stat-label">新客户订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-info">{{ statistics.avgWaitTime }}</div>
            <div class="stat-label">平均等待时间</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 订单分配对话框 -->
    <order-assignment-dialog 
      :visible.sync="assignmentVisible" 
      :order-ids="selectedOrderIds"
      @refresh="getList"
    />

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getUnassignedOrders, grabOrder, checkCanGrab } from "@/api/crm/order";
import OrderAssignmentDialog from "./OrderAssignmentDialog";

export default {
  name: "UnassignedOrdersDialog",
  dicts: ['crm_order_source'],
  components: {
    OrderAssignmentDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      orderList: [],
      selectedOrders: [],
      selectedOrderIds: [],
      assignmentVisible: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        customerName: null,
        priorityLevel: null,
        assignmentStatus: 'UNASSIGNED'
      },
      statistics: {
        totalCount: 0,
        urgentCount: 0,
        newCustomerCount: 0,
        avgWaitTime: '0小时'
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    hasSelection() {
      return this.selectedOrders.length > 0;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.getList();
        this.getStatistics();
      }
    }
  },
  methods: {
    /** 查询未分配订单列表 */
    getList() {
      this.loading = true;
      // TODO: 调用未分配订单API
      // 模拟数据
      setTimeout(() => {
        this.orderList = Array.from({ length: 10 }, (_, index) => ({
          id: index + 1,
          orderNo: `ORD${Date.now()}${index}`,
          customerName: `客户${index + 1}`,
          customerPhone: `138000${index + 1}0000`,
          totalAmount: (Math.random() * 10000).toFixed(2),
          orderSource: ['3D_PRINTING', 'MANUAL', 'ONLINE', 'PHONE'][Math.floor(Math.random() * 4)],
          priorityLevel: ['LOW', 'NORMAL', 'HIGH', 'URGENT'][Math.floor(Math.random() * 4)],
          isNewCustomer: Math.random() > 0.5,
          waitTime: `${Math.floor(Math.random() * 24)}小时`,
          createTime: new Date(Date.now() - Math.random() * 86400000 * 7)
        }));
        this.total = 50;
        this.loading = false;
      }, 500);
    },

    /** 获取统计信息 */
    getStatistics() {
      // TODO: 调用统计API
      this.statistics = {
        totalCount: 50,
        urgentCount: 8,
        newCustomerCount: 15,
        avgWaitTime: '3.5小时'
      };
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedOrders = selection;
      this.selectedOrderIds = selection.map(item => item.id);
    },

    /** 查看订单详情 */
    handleView(row) {
      this.$emit('view', row.id);
    },

    /** 分配单个订单 */
    handleAssign(row) {
      this.selectedOrderIds = [row.id];
      this.assignmentVisible = true;
    },

    /** 批量分配 */
    handleBatchAssign() {
      if (this.selectedOrderIds.length === 0) {
        this.$modal.msgError("请选择要分配的订单");
        return;
      }
      this.assignmentVisible = true;
    },

    /** 抢单 */
    handleGrab(row) {
      this.$modal.confirm(`确认抢单订单"${row.orderNo}"？`).then(() => {
        return grabOrder(row.id);
      }).then(() => {
        this.$modal.msgSuccess("抢单成功");
        this.getList();
        this.$emit('refresh');
      }).catch(() => {});
    },

    /** 批量抢单 */
    handleBatchGrab() {
      if (this.selectedOrderIds.length === 0) {
        this.$modal.msgError("请选择要抢单的订单");
        return;
      }
      
      this.$modal.confirm(`确认抢单选中的${this.selectedOrderIds.length}个订单？`).then(() => {
        // TODO: 实现批量抢单
        this.$modal.msgSuccess("批量抢单成功");
        this.getList();
        this.$emit('refresh');
      }).catch(() => {});
    },

    /** 智能分配 */
    handleAutoAssign() {
      this.$modal.confirm('确认对所有未分配订单进行智能分配？').then(() => {
        // TODO: 实现智能分配
        this.$modal.msgSuccess("智能分配完成");
        this.getList();
        this.$emit('refresh');
      }).catch(() => {});
    },

    /** 优先级排序 */
    handleSort() {
      this.orderList.sort((a, b) => {
        const priorityOrder = { 'URGENT': 4, 'HIGH': 3, 'NORMAL': 2, 'LOW': 1 };
        return priorityOrder[b.priorityLevel] - priorityOrder[a.priorityLevel];
      });
    },

    /** 检查是否可以抢单 */
    canGrab(row) {
      // TODO: 调用检查抢单权限API
      return true;
    },

    /** 获取行样式类名 */
    getRowClassName({ row }) {
      if (row.priorityLevel === 'URGENT') {
        return 'urgent-row';
      } else if (row.priorityLevel === 'HIGH') {
        return 'high-row';
      }
      return '';
    },

    /** 获取等待时间样式类名 */
    getWaitTimeClass(waitTime) {
      const hours = parseInt(waitTime);
      if (hours > 24) return 'text-danger';
      if (hours > 12) return 'text-warning';
      return 'text-success';
    },

    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.selectedOrders = [];
      this.selectedOrderIds = [];
      this.orderList = [];
    }
  }
};
</script>

<style scoped>
.text-muted {
  color: #999;
  font-size: 12px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-info {
  color: #409eff;
  font-weight: bold;
}

.mb8 {
  margin-bottom: 8px;
}

.mt-3 {
  margin-top: 20px;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

/* 表格行样式 */
::v-deep .urgent-row {
  background-color: #fef0f0;
}

::v-deep .high-row {
  background-color: #fdf6ec;
}
</style>

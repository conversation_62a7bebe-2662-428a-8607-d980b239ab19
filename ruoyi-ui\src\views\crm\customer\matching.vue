<template>
  <div class="app-container">
    <!-- 客户匹配工具 -->
    <el-card class="mb-3">
      <div slot="header" class="clearfix">
        <span class="card-title">客户匹配工具</span>
      </div>
      
      <el-form :model="matchForm" ref="matchForm" :inline="true" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户电话" prop="phone">
              <el-input 
                v-model="matchForm.phone" 
                placeholder="请输入客户电话"
                clearable
                @keyup.enter.native="handlePhoneMatch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称" prop="customerName">
              <el-input 
                v-model="matchForm.customerName" 
                placeholder="请输入客户名称"
                clearable
                @keyup.enter.native="handleNameMatch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户邮箱" prop="email">
              <el-input 
                v-model="matchForm.email" 
                placeholder="请输入客户邮箱"
                clearable
                @keyup.enter.native="handleEmailMatch"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="公司名称" prop="companyName">
              <el-input 
                v-model="matchForm.companyName" 
                placeholder="请输入公司名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户地址" prop="address">
              <el-input 
                v-model="matchForm.address" 
                placeholder="请输入客户地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属行业" prop="industry">
              <el-select v-model="matchForm.industry" placeholder="请选择行业" clearable>
                <el-option label="制造业" value="MANUFACTURING" />
                <el-option label="服务业" value="SERVICE" />
                <el-option label="科技行业" value="TECHNOLOGY" />
                <el-option label="金融业" value="FINANCE" />
                <el-option label="教育行业" value="EDUCATION" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24" style="text-align: center;">
            <el-button type="primary" @click="handlePhoneMatch" :loading="phoneLoading">电话匹配</el-button>
            <el-button type="success" @click="handleNameMatch" :loading="nameLoading">名称匹配</el-button>
            <el-button type="warning" @click="handleEmailMatch" :loading="emailLoading">邮箱匹配</el-button>
            <el-button type="info" @click="handleComprehensiveMatch" :loading="comprehensiveLoading">综合匹配</el-button>
            <el-button type="primary" plain @click="handleSmartMatch" :loading="smartLoading">智能匹配</el-button>
            <el-button @click="resetMatchForm">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 匹配结果 -->
    <el-card class="mb-3" v-if="matchResults.length > 0">
      <div slot="header" class="clearfix">
        <span class="card-title">匹配结果 ({{ matchResults.length }}个)</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text"
          @click="clearResults"
        >清空结果</el-button>
      </div>
      
      <el-table :data="matchResults" border>
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="匹配类型" prop="matchType" width="120" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.matchType === 'EXACT_PHONE'" type="success">精确电话</el-tag>
            <el-tag v-else-if="scope.row.matchType === 'FUZZY_PHONE'" type="warning">模糊电话</el-tag>
            <el-tag v-else-if="scope.row.matchType === 'EXACT_EMAIL'" type="success">精确邮箱</el-tag>
            <el-tag v-else-if="scope.row.matchType === 'EXACT_NAME'" type="success">精确名称</el-tag>
            <el-tag v-else-if="scope.row.matchType === 'FUZZY_NAME'" type="warning">模糊名称</el-tag>
            <el-tag v-else-if="scope.row.matchType === 'COMPREHENSIVE'" type="primary">综合匹配</el-tag>
            <el-tag v-else type="info">其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="置信度" prop="confidence" width="100" align="center">
          <template slot-scope="scope">
            <el-progress 
              :percentage="scope.row.confidence" 
              :color="getConfidenceColor(scope.row.confidence)"
              :show-text="false"
            />
            <span class="confidence-text">{{ scope.row.confidence }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="客户信息" align="center">
          <template slot-scope="scope">
            <div class="customer-info">
              <div><strong>{{ scope.row.customer.customerName }}</strong></div>
              <div class="text-muted">{{ scope.row.customer.customerPhone }}</div>
              <div class="text-muted">{{ scope.row.customer.customerEmail }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="公司信息" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.customer.companyName || '未填写' }}</div>
            <div class="text-muted">{{ scope.row.customer.customerAddress || '未填写' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="负责人" prop="customer.responsiblePersonId" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.customer.responsiblePersonId || '未分配' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="customer.createTime" width="160" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.customer.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewCustomer(scope.row.customer)">查看详情</el-button>
            <el-button size="mini" type="text" @click="useCustomer(scope.row.customer)">使用客户</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新客户检测 -->
    <el-card class="mb-3">
      <div slot="header" class="clearfix">
        <span class="card-title">新客户检测</span>
      </div>
      
      <el-form :model="newCustomerForm" ref="newCustomerForm" :inline="true" label-width="100px">
        <el-form-item label="客户电话" prop="phone">
          <el-input 
            v-model="newCustomerForm.phone" 
            placeholder="请输入客户电话"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户邮箱" prop="email">
          <el-input 
            v-model="newCustomerForm.email" 
            placeholder="请输入客户邮箱"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input 
            v-model="newCustomerForm.customerName" 
            placeholder="请输入客户名称"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="checkNewCustomer" :loading="newCustomerLoading">检测新客户</el-button>
          <el-button @click="resetNewCustomerForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="newCustomerResult !== null" class="new-customer-result">
        <el-alert
          :title="newCustomerResult ? '这是新客户' : '这是老客户'"
          :type="newCustomerResult ? 'success' : 'warning'"
          :description="newCustomerResult ? '系统中未找到匹配的客户记录，建议创建新客户档案。' : '系统中已存在相似的客户记录，建议使用现有客户。'"
          show-icon
          :closable="false"
        />
      </div>
    </el-card>

    <!-- 重复客户检测 -->
    <el-card class="mb-3">
      <div slot="header" class="clearfix">
        <span class="card-title">重复客户检测</span>
      </div>
      
      <el-form :model="duplicateForm" ref="duplicateForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户电话" prop="phone">
              <el-input v-model="duplicateForm.phone" placeholder="请输入客户电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户邮箱" prop="email">
              <el-input v-model="duplicateForm.email" placeholder="请输入客户邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="duplicateForm.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="duplicateForm.companyName" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="danger" @click="checkDuplicate" :loading="duplicateLoading">检测重复</el-button>
          <el-button @click="resetDuplicateForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="duplicateResult" class="duplicate-result">
        <el-alert
          :title="duplicateResult.hasDuplicate ? `发现${duplicateResult.duplicateCount}个重复客户` : '未发现重复客户'"
          :type="duplicateResult.hasDuplicate ? 'warning' : 'success'"
          :description="duplicateResult.hasDuplicate ? '系统检测到可能重复的客户记录，请仔细核对。' : '未检测到重复的客户记录。'"
          show-icon
          :closable="false"
        />
        
        <el-table 
          v-if="duplicateResult.hasDuplicate" 
          :data="duplicateResult.duplicates" 
          border 
          class="mt-3"
        >
          <el-table-column label="客户名称" prop="customer.customerName" />
          <el-table-column label="客户电话" prop="customer.customerPhone" />
          <el-table-column label="客户邮箱" prop="customer.customerEmail" />
          <el-table-column label="相似度" prop="confidence" width="100">
            <template slot-scope="scope">
              <span class="confidence-text">{{ scope.row.confidence }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="viewCustomer(scope.row.customer)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 匹配统计 -->
    <el-card>
      <div slot="header" class="clearfix">
        <span class="card-title">匹配统计</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-primary">{{ statistics.totalMatches }}</div>
            <div class="stat-label">总匹配次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-success">{{ statistics.successMatches }}</div>
            <div class="stat-label">成功匹配</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-warning">{{ statistics.newCustomers }}</div>
            <div class="stat-label">新客户数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value text-danger">{{ statistics.duplicates }}</div>
            <div class="stat-label">重复客户</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { 
  matchCustomerByPhone, 
  matchCustomerByName, 
  matchCustomerByEmail,
  matchCustomerComprehensive,
  smartMatchCustomer,
  checkNewCustomer,
  checkDuplicateCustomer
} from "@/api/crm/order";

export default {
  name: "CustomerMatching",
  data() {
    return {
      // 匹配表单
      matchForm: {
        phone: '',
        customerName: '',
        email: '',
        companyName: '',
        address: '',
        industry: ''
      },
      // 新客户检测表单
      newCustomerForm: {
        phone: '',
        email: '',
        customerName: ''
      },
      // 重复检测表单
      duplicateForm: {
        phone: '',
        email: '',
        customerName: '',
        companyName: ''
      },
      // 加载状态
      phoneLoading: false,
      nameLoading: false,
      emailLoading: false,
      comprehensiveLoading: false,
      smartLoading: false,
      newCustomerLoading: false,
      duplicateLoading: false,
      // 结果数据
      matchResults: [],
      newCustomerResult: null,
      duplicateResult: null,
      // 统计数据
      statistics: {
        totalMatches: 0,
        successMatches: 0,
        newCustomers: 0,
        duplicates: 0
      }
    };
  },
  created() {
    this.loadStatistics();
  },
  methods: {
    /** 电话匹配 */
    handlePhoneMatch() {
      if (!this.matchForm.phone) {
        this.$modal.msgError("请输入客户电话");
        return;
      }
      
      this.phoneLoading = true;
      matchCustomerByPhone(this.matchForm.phone).then(response => {
        if (response.data.matched) {
          this.matchResults = [response.data];
          this.$modal.msgSuccess("匹配成功");
        } else {
          this.matchResults = [];
          this.$modal.msgInfo("未找到匹配的客户");
        }
        this.updateStatistics();
      }).finally(() => {
        this.phoneLoading = false;
      });
    },

    /** 名称匹配 */
    handleNameMatch() {
      if (!this.matchForm.customerName) {
        this.$modal.msgError("请输入客户名称");
        return;
      }
      
      this.nameLoading = true;
      matchCustomerByName(this.matchForm.customerName).then(response => {
        this.matchResults = response.data || [];
        if (this.matchResults.length > 0) {
          this.$modal.msgSuccess(`找到${this.matchResults.length}个匹配结果`);
        } else {
          this.$modal.msgInfo("未找到匹配的客户");
        }
        this.updateStatistics();
      }).finally(() => {
        this.nameLoading = false;
      });
    },

    /** 邮箱匹配 */
    handleEmailMatch() {
      if (!this.matchForm.email) {
        this.$modal.msgError("请输入客户邮箱");
        return;
      }
      
      this.emailLoading = true;
      matchCustomerByEmail(this.matchForm.email).then(response => {
        if (response.data.matched) {
          this.matchResults = [response.data];
          this.$modal.msgSuccess("匹配成功");
        } else {
          this.matchResults = [];
          this.$modal.msgInfo("未找到匹配的客户");
        }
        this.updateStatistics();
      }).finally(() => {
        this.emailLoading = false;
      });
    },

    /** 综合匹配 */
    handleComprehensiveMatch() {
      if (!this.matchForm.phone && !this.matchForm.email && !this.matchForm.customerName) {
        this.$modal.msgError("请至少输入一个匹配条件");
        return;
      }
      
      this.comprehensiveLoading = true;
      matchCustomerComprehensive(
        this.matchForm.phone, 
        this.matchForm.email, 
        this.matchForm.customerName
      ).then(response => {
        if (response.data.matched) {
          this.matchResults = [response.data];
          this.$modal.msgSuccess("综合匹配成功");
        } else {
          this.matchResults = [];
          this.$modal.msgInfo("综合匹配未找到客户");
        }
        this.updateStatistics();
      }).finally(() => {
        this.comprehensiveLoading = false;
      });
    },

    /** 智能匹配 */
    handleSmartMatch() {
      this.smartLoading = true;
      smartMatchCustomer(this.matchForm).then(response => {
        this.matchResults = response.data || [];
        if (this.matchResults.length > 0) {
          this.$modal.msgSuccess(`智能匹配找到${this.matchResults.length}个结果`);
        } else {
          this.$modal.msgInfo("智能匹配未找到客户");
        }
        this.updateStatistics();
      }).finally(() => {
        this.smartLoading = false;
      });
    },

    /** 检测新客户 */
    checkNewCustomer() {
      if (!this.newCustomerForm.phone && !this.newCustomerForm.email && !this.newCustomerForm.customerName) {
        this.$modal.msgError("请至少输入一个检测条件");
        return;
      }
      
      this.newCustomerLoading = true;
      checkNewCustomer(
        this.newCustomerForm.phone,
        this.newCustomerForm.email,
        this.newCustomerForm.customerName
      ).then(response => {
        this.newCustomerResult = response.data;
        this.updateStatistics();
      }).finally(() => {
        this.newCustomerLoading = false;
      });
    },

    /** 检测重复客户 */
    checkDuplicate() {
      this.duplicateLoading = true;
      checkDuplicateCustomer(this.duplicateForm).then(response => {
        this.duplicateResult = response.data;
        this.updateStatistics();
      }).finally(() => {
        this.duplicateLoading = false;
      });
    },

    /** 查看客户详情 */
    viewCustomer(customer) {
      // TODO: 打开客户详情对话框
      this.$modal.msgInfo("查看客户详情功能开发中");
    },

    /** 使用客户 */
    useCustomer(customer) {
      this.$modal.confirm(`确认使用客户"${customer.customerName}"？`).then(() => {
        // TODO: 实现使用客户逻辑
        this.$modal.msgSuccess("已选择该客户");
      });
    },

    /** 获取置信度颜色 */
    getConfidenceColor(confidence) {
      if (confidence >= 90) return '#67c23a';
      if (confidence >= 70) return '#e6a23c';
      if (confidence >= 50) return '#f56c6c';
      return '#909399';
    },

    /** 清空结果 */
    clearResults() {
      this.matchResults = [];
      this.newCustomerResult = null;
      this.duplicateResult = null;
    },

    /** 重置匹配表单 */
    resetMatchForm() {
      this.matchForm = {
        phone: '',
        customerName: '',
        email: '',
        companyName: '',
        address: '',
        industry: ''
      };
      this.clearResults();
    },

    /** 重置新客户表单 */
    resetNewCustomerForm() {
      this.newCustomerForm = {
        phone: '',
        email: '',
        customerName: ''
      };
      this.newCustomerResult = null;
    },

    /** 重置重复检测表单 */
    resetDuplicateForm() {
      this.duplicateForm = {
        phone: '',
        email: '',
        customerName: '',
        companyName: ''
      };
      this.duplicateResult = null;
    },

    /** 更新统计数据 */
    updateStatistics() {
      this.statistics.totalMatches++;
      if (this.matchResults.length > 0) {
        this.statistics.successMatches++;
      }
      if (this.newCustomerResult === true) {
        this.statistics.newCustomers++;
      }
      if (this.duplicateResult && this.duplicateResult.hasDuplicate) {
        this.statistics.duplicates++;
      }
    },

    /** 加载统计数据 */
    loadStatistics() {
      // TODO: 从API加载统计数据
      this.statistics = {
        totalMatches: 156,
        successMatches: 128,
        newCustomers: 45,
        duplicates: 12
      };
    }
  }
};
</script>

<style scoped>
.card-title {
  font-weight: bold;
  font-size: 16px;
}

.mb-3 {
  margin-bottom: 20px;
}

.mt-3 {
  margin-top: 20px;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.text-primary {
  color: #409eff;
  font-weight: bold;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.customer-info {
  text-align: left;
}

.confidence-text {
  font-size: 12px;
  margin-left: 5px;
}

.new-customer-result,
.duplicate-result {
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}
</style>
